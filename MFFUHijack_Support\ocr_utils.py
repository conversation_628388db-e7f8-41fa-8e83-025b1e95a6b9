"""
OCR Utilities Module for MFFUHijack
Handles OCR model loading, text extraction, regex matching, and image processing
"""

import re
import os
import cv2
import numpy as np
import json
from typing import Tuple, List, Optional, Dict, Any
import logging

# OCR Libraries
try:
    import easyocr
except ImportError:
    easyocr = None

try:
    import pytesseract
    from PIL import Image
    TESSERACT_AVAILABLE = True
except ImportError:
    pytesseract = None
    TESSERACT_AVAILABLE = False

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class OCREngine:
    """Base class for OCR engines"""
    
    def __init__(self):
        self.name = "base"
        self.model = None
        
    def extract_text(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """Extract text from image. Returns list of detection results."""
        raise NotImplementedError
        
    def is_available(self) -> bool:
        """Check if this OCR engine is available"""
        return self.model is not None


class EasyOCREngine(OCREngine):
    """EasyOCR implementation with GPU acceleration"""

    def __init__(self):
        super().__init__()
        self.name = "EasyOCR"
        self.gpu_available = self._check_gpu_availability()
        self.model = None
        self.using_gpu = False

        # Lazy initialization - only create model when first used
        self._model_initialized = False

    def _initialize_model(self):
        """Initialize EasyOCR model with GPU/CPU detection"""
        if self._model_initialized:
            return

        try:
            if not easyocr:
                logger.warning("EasyOCR not available")
                return

            # Try GPU first if available, fallback to CPU
            if self.gpu_available:
                try:
                    logger.info("Initializing EasyOCR with GPU acceleration...")
                    self.model = easyocr.Reader(['en'], gpu=True, verbose=False)
                    self.using_gpu = True
                    logger.info("✅ EasyOCR initialized successfully with GPU acceleration")
                except Exception as gpu_error:
                    logger.warning(f"GPU initialization failed, falling back to CPU: {gpu_error}")
                    try:
                        self.model = easyocr.Reader(['en'], gpu=False, verbose=False)
                        self.using_gpu = False
                        logger.info("✅ EasyOCR initialized successfully with CPU")
                    except Exception as cpu_error:
                        logger.error(f"CPU fallback also failed: {cpu_error}")
                        self.model = None
            else:
                try:
                    logger.info("Initializing EasyOCR with CPU (no GPU detected)...")
                    self.model = easyocr.Reader(['en'], gpu=False, verbose=False)
                    self.using_gpu = False
                    logger.info("✅ EasyOCR initialized successfully with CPU")
                except Exception as e:
                    logger.error(f"CPU initialization failed: {e}")
                    self.model = None

        except Exception as e:
            logger.error(f"Failed to initialize EasyOCR: {e}")
            self.model = None
        finally:
            self._model_initialized = True

    def _check_gpu_availability(self) -> bool:
        """Check if GPU is available for EasyOCR"""
        try:
            import torch
            if torch.cuda.is_available():
                gpu_count = torch.cuda.device_count()
                gpu_name = torch.cuda.get_device_name(0) if gpu_count > 0 else "Unknown"
                logger.info(f"GPU detected: {gpu_name} (CUDA available)")
                return True
            else:
                logger.info("No CUDA-capable GPU detected")
                return False
        except ImportError:
            logger.info("PyTorch not available, using CPU for EasyOCR")
            return False
        except Exception as e:
            logger.warning(f"GPU detection failed: {e}")
            return False

    def get_gpu_info(self) -> dict:
        """Get GPU information for performance monitoring"""
        # Initialize model if needed to get accurate status
        if not self._model_initialized:
            self._initialize_model()

        info = {
            'gpu_available': self.gpu_available,
            'using_gpu': self.using_gpu and self.model is not None,
            'device': 'GPU' if self.using_gpu else 'CPU',
            'model_initialized': self._model_initialized
        }

        if self.using_gpu:
            try:
                import torch
                if torch.cuda.is_available():
                    info.update({
                        'gpu_name': torch.cuda.get_device_name(0),
                        'gpu_memory_total': f"{torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB",
                        'gpu_memory_allocated': f"{torch.cuda.memory_allocated(0) / 1024**3:.1f} GB",
                        'cuda_version': torch.version.cuda
                    })
            except Exception as e:
                info['gpu_error'] = str(e)

        return info
    
    def preprocess_for_livestream_text(self, image: np.ndarray) -> np.ndarray:
        """
        Minimal preprocessing optimized for livestream text recognition
        Focuses on preserving original text quality while improving readability
        """
        print(f"🎨 Minimal preprocessing for livestream text OCR...")

        # Convert to grayscale if needed
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()

        # Apply enhanced preprocessing for better text recognition
        height, width = gray.shape

        # Apply CLAHE for better contrast
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(4, 4))
        enhanced = clahe.apply(gray)

        # Apply bilateral filter to reduce noise while preserving text edges
        filtered = cv2.bilateralFilter(enhanced, 9, 75, 75)

        # Scale up if image is small for better OCR
        if height < 50 or width < 200:
            scale_factor = max(2, 50 // height)
            scaled = cv2.resize(filtered, (width * scale_factor, height * scale_factor), interpolation=cv2.INTER_CUBIC)
            print(f"   ✅ Enhanced and scaled image {scale_factor}x: {width}x{height} -> {scaled.shape[1]}x{scaled.shape[0]}")
            return scaled
        else:
            print(f"   ✅ Enhanced image: {width}x{height}")
            return filtered

    def extract_text(self, image: np.ndarray, use_raw_image: bool = False) -> List[Dict[str, Any]]:
        """Extract text using EasyOCR with optional preprocessing"""
        # Initialize model on first use
        if not self._model_initialized:
            self._initialize_model()

        if not self.model:
            return []

        try:
            if use_raw_image:
                # Use raw image without any preprocessing for best accuracy
                print(f"🎨 Using raw image for OCR (no preprocessing)")
                processed_image = image
            else:
                # Use enhanced preprocessing for better text recognition
                processed_image = self.preprocess_for_livestream_text(image)

            # Use EasyOCR with enhanced settings for livestream text detection
            print(f"🔍 Running EasyOCR with enhanced settings...")
            results = self.model.readtext(
                processed_image,
                detail=1,  # Return detailed results
                paragraph=True,  # Group text into paragraphs for better context
                width_ths=0.5,  # Lower threshold for better word separation
                height_ths=0.5,  # Lower threshold for better line separation
                decoder='beamsearch',  # Use beam search for better accuracy
                beamWidth=15,  # Higher beam width for more alternatives
                batch_size=1,  # Process one image at a time
                # Remove allowlist to capture spaces, colons, and special characters
                text_threshold=0.4,  # Lower text detection threshold
                link_threshold=0.2,  # Lower link threshold for better word grouping
                low_text=0.2,  # Lower text confidence threshold
                canvas_size=3840,  # Larger canvas for better resolution
                mag_ratio=2.0  # Higher magnification ratio for better character recognition
            )
            print(f"🔍 EasyOCR completed, processing {len(results)} results...")

            print(f"🔍 EasyOCR found {len(results)} text regions")

            # Convert to standard format
            formatted_results = []
            for result in results:
                try:
                    # Handle different result formats from EasyOCR
                    if len(result) == 3:
                        bbox, text, confidence = result
                    elif len(result) == 2:
                        bbox, text = result
                        confidence = 0.0  # Mark as unknown confidence when not provided
                        print(f"   ⚠️ No confidence provided for text: '{text.strip()}'")
                    else:
                        print(f"   ⚠️ Unexpected result format: {result}")
                        continue

                    # Use lower confidence threshold to capture more text
                    if confidence >= 0.2:  # Lower threshold for livestream text
                        formatted_results.append({
                            'text': text.strip(),
                            'confidence': confidence,
                            'bbox': bbox
                        })
                        print(f"   📝 Text: '{text.strip()}' (confidence: {confidence:.3f})")
                    else:
                        print(f"   ❌ Filtered low confidence: '{text.strip()}' (confidence: {confidence:.3f})")
                except Exception as e:
                    print(f"   ⚠️ Error processing result {result}: {e}")
                    continue

            print(f"✅ EasyOCR returned {len(formatted_results)} high-confidence results")
            return formatted_results

        except Exception as e:
            logger.error(f"EasyOCR extraction failed: {e}")
            print(f"❌ EasyOCR error: {e}")
            print(f"   Image shape: {image.shape if hasattr(image, 'shape') else 'unknown'}")
            print(f"   Model available: {self.model is not None}")
            print(f"   Using GPU: {self.using_gpu}")

            # Try fallback with simpler settings
            try:
                print(f"🔄 Attempting fallback OCR with simpler settings...")
                fallback_results = self.model.readtext(
                    image,  # Use original image
                    detail=1,
                    paragraph=False,  # Disable paragraph mode
                    width_ths=0.7,
                    height_ths=0.7
                )

                # Process fallback results
                fallback_formatted = []
                for result in fallback_results:
                    try:
                        if len(result) == 3:
                            bbox, text, confidence = result
                        elif len(result) == 2:
                            bbox, text = result
                            confidence = 0.5  # Default confidence for fallback
                        else:
                            continue

                        if confidence >= 0.3 and text.strip():
                            fallback_formatted.append({
                                'text': text.strip(),
                                'confidence': confidence,
                                'bbox': bbox
                            })
                            print(f"   📝 Fallback text: '{text.strip()}' (confidence: {confidence:.3f})")
                    except Exception as fallback_error:
                        print(f"   ⚠️ Error processing fallback result: {fallback_error}")
                        continue

                print(f"✅ Fallback OCR returned {len(fallback_formatted)} results")
                return fallback_formatted

            except Exception as fallback_error:
                logger.error(f"Fallback OCR also failed: {fallback_error}")
                print(f"❌ Fallback OCR also failed: {fallback_error}")
                return []



class TesseractOCREngine(OCREngine):
    """Tesseract OCR implementation with enhanced configuration"""

    def __init__(self):
        super().__init__()
        self.name = "Tesseract"
        self.model = None
        self._model_initialized = False

        # Check if Tesseract is available
        if TESSERACT_AVAILABLE:
            self._initialize_model()

    def _initialize_model(self):
        """Initialize Tesseract OCR"""
        if self._model_initialized:
            return

        try:
            if not TESSERACT_AVAILABLE:
                logger.warning("Tesseract not available")
                return

            # Test Tesseract installation
            try:
                # Try to get Tesseract version to verify it's working
                version = pytesseract.get_tesseract_version()
                logger.info(f"✅ Tesseract OCR initialized successfully (version: {version})")
                self.model = True  # Mark as available
            except Exception as e:
                logger.error(f"Tesseract initialization failed: {e}")
                logger.error("Make sure Tesseract is installed and in your PATH")
                logger.error("Windows: Download from https://github.com/UB-Mannheim/tesseract/wiki")
                logger.error("macOS: brew install tesseract")
                logger.error("Linux: sudo apt install tesseract-ocr")
                self.model = None

        except Exception as e:
            logger.error(f"Failed to initialize Tesseract: {e}")
            self.model = None
        finally:
            self._model_initialized = True

    def is_available(self) -> bool:
        """Check if Tesseract is available"""
        if not self._model_initialized:
            self._initialize_model()
        return self.model is not None

    def preprocess_for_tesseract(self, image: np.ndarray) -> np.ndarray:
        """Preprocess image specifically for Tesseract OCR"""
        try:
            # Convert to grayscale if needed
            if len(image.shape) == 3:
                gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            else:
                gray = image.copy()

            # Apply Gaussian blur to reduce noise
            blurred = cv2.GaussianBlur(gray, (3, 3), 0)

            # Apply adaptive thresholding for better text contrast
            thresh = cv2.adaptiveThreshold(
                blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, cv2.THRESH_BINARY, 11, 2
            )

            # Apply morphological operations to clean up the image
            kernel = np.ones((2, 2), np.uint8)
            cleaned = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel)

            # Scale up the image for better OCR accuracy
            height, width = cleaned.shape
            scaled = cv2.resize(cleaned, (width * 2, height * 2), interpolation=cv2.INTER_CUBIC)

            return scaled

        except Exception as e:
            logger.error(f"Tesseract preprocessing failed: {e}")
            return image

    def extract_text(self, image: np.ndarray, use_raw_image: bool = False) -> List[Dict[str, Any]]:
        """Extract text using Tesseract OCR"""
        # Initialize model on first use
        if not self._model_initialized:
            self._initialize_model()

        if not self.model:
            return []

        try:
            if use_raw_image:
                print(f"🎨 Using raw image for Tesseract OCR (no preprocessing)")
                processed_image = image
            else:
                # Use enhanced preprocessing for better text recognition
                processed_image = self.preprocess_for_tesseract(image)

            # Convert numpy array to PIL Image
            if len(processed_image.shape) == 3:
                pil_image = Image.fromarray(cv2.cvtColor(processed_image, cv2.COLOR_BGR2RGB))
            else:
                pil_image = Image.fromarray(processed_image)

            print(f"🔍 Running Tesseract OCR with enhanced settings...")

            # Configure Tesseract for livestream text detection
            # PSM 6: Uniform block of text
            # PSM 8: Single word
            # PSM 13: Raw line. Treat the image as a single text line
            custom_config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789:\ '

            # Get detailed data with bounding boxes and confidence scores
            data = pytesseract.image_to_data(pil_image, config=custom_config, output_type=pytesseract.Output.DICT)

            results = []
            n_boxes = len(data['level'])

            for i in range(n_boxes):
                confidence = float(data['conf'][i])
                text = data['text'][i].strip()

                # Filter out low confidence and empty results
                if confidence > 30 and text:  # Lower threshold for Tesseract
                    # Get bounding box coordinates
                    x, y, w, h = data['left'][i], data['top'][i], data['width'][i], data['height'][i]

                    # Scale coordinates back if we scaled the image
                    if not use_raw_image:
                        x, y, w, h = x // 2, y // 2, w // 2, h // 2

                    bbox = [[x, y], [x + w, y], [x + w, y + h], [x, y + h]]

                    results.append({
                        'text': text,
                        'confidence': confidence / 100.0,  # Convert to 0-1 scale
                        'bbox': bbox
                    })

                    print(f"   📝 Tesseract text: '{text}' (confidence: {confidence:.1f}%)")

            print(f"✅ Tesseract OCR returned {len(results)} results")
            return results

        except Exception as e:
            logger.error(f"Tesseract extraction failed: {e}")
            print(f"❌ Tesseract error: {e}")
            print(f"   Image shape: {image.shape if hasattr(image, 'shape') else 'unknown'}")
            print(f"   Model available: {self.model is not None}")

            # Try fallback with simpler settings
            try:
                print(f"🔄 Attempting Tesseract fallback with simpler settings...")

                # Convert to PIL Image
                if len(image.shape) == 3:
                    pil_image = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
                else:
                    pil_image = Image.fromarray(image)

                # Simple OCR without custom config
                simple_text = pytesseract.image_to_string(pil_image).strip()

                if simple_text:
                    # Return simple result without bounding box
                    fallback_results = [{
                        'text': simple_text,
                        'confidence': 0.5,  # Default confidence for fallback
                        'bbox': [[0, 0], [image.shape[1], 0], [image.shape[1], image.shape[0]], [0, image.shape[0]]]
                    }]
                    print(f"✅ Tesseract fallback returned: '{simple_text}'")
                    return fallback_results

            except Exception as fallback_error:
                logger.error(f"Tesseract fallback also failed: {fallback_error}")
                print(f"❌ Tesseract fallback also failed: {fallback_error}")
                return []

            return []


class CustomOCREngine(OCREngine):
    """Custom trained OCR model"""
    
    def __init__(self, model_path: str = "ocr_models/custom_model.pth"):
        super().__init__()
        self.name = "Custom"
        self.model_path = model_path

        if os.path.exists(model_path):
            try:
                # TODO: Implement custom model loading
                # This will be implemented in the model training phase
                logger.info(f"Custom model loaded from {model_path}")
            except Exception as e:
                logger.error(f"Failed to load custom model: {e}")
                self.model = None
        else:
            # Don't log warning on startup - custom model is optional
            self.model = None
    
    def extract_text(self, image: np.ndarray) -> List[Dict[str, Any]]:
        """Extract text using custom model"""
        if not self.model:
            return []

        # TODO: Implement custom model inference
        logger.warning("Custom model inference not yet implemented")
        return []

    def is_available(self) -> bool:
        """Check if custom model is available"""
        return os.path.exists(self.model_path) and self.model is not None


def create_regex_from_patterns(patterns: List[str]) -> re.Pattern:
    """Create a regex pattern from user-defined text patterns"""
    if not patterns:
        return re.compile(r"(?!.*)", re.IGNORECASE)  # Pattern that never matches

    regex_parts = []
    for pattern in patterns:
        # Escape special regex characters except our placeholders
        escaped_pattern = re.escape(pattern)

        # Replace our placeholders with regex groups
        # {code} -> capture group for alphanumeric codes (only placeholder we use now)
        escaped_pattern = escaped_pattern.replace(r"\{code\}", r"([A-Z0-9\-]+)")

        regex_parts.append(escaped_pattern)

    # Combine all patterns with OR
    combined_pattern = "|".join(f"({part})" for part in regex_parts)
    return re.compile(combined_pattern, re.IGNORECASE)


def load_pattern_config() -> Dict:
    """Load pattern configuration from file"""
    config_file = "pattern_config.json"
    default_config = {
        "Free Reset Code": {
            "enabled": True,
            "patterns": [
                "FREE RESETS USE CODE: {code}",
                "FREE RESET USE CODE: {code}",
                "RESETS USE CODE: {code}",
                "RESET CODE: {code}"
            ]
        },
        "Starter": {
            "enabled": True,
            "patterns": [
                "STARTER ACCOUNTS USE CODE: {code}",
                "STARTER ACCOUNT USE CODE: {code}",
                "STARTER USE CODE: {code}",
                "STARTER: {code}"
            ]
        },
        "Starter Plus": {
            "enabled": True,
            "patterns": [
                "STARTER+ ACCOUNTS USE CODE: {code}",
                "STARTER PLUS ACCOUNTS USE CODE: {code}",
                "STARTER+ USE CODE: {code}",
                "STARTER PLUS: {code}",
                "STARTER+: {code}"
            ]
        },
        "Expert": {
            "enabled": True,
            "patterns": [
                "EXPERT ACCOUNTS USE CODE: {code}",
                "EXPERT ACCOUNT USE CODE: {code}",
                "EXPERT USE CODE: {code}",
                "EXPERT: {code}"
            ]
        }
    }

    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error loading pattern config: {e}")

    return default_config


class OCRManager:
    """Manages multiple OCR engines and provides unified interface"""

    def __init__(self):
        self.engines = {
            'Tesseract': TesseractOCREngine(),
            'EasyOCR': EasyOCREngine(),
            'Custom': CustomOCREngine()
        }

        # Set Tesseract as default, fallback to EasyOCR if not available
        if self.engines['Tesseract'].is_available():
            self.current_engine = 'Tesseract'
        elif self.engines['EasyOCR'].is_available():
            self.current_engine = 'EasyOCR'
        else:
            self.current_engine = 'Custom'
        self.min_confidence = 0.8

        # Load custom patterns
        self.pattern_config = load_pattern_config()
        self.update_patterns()

        # Log GPU status on initialization
        self.log_gpu_status()

    def update_patterns(self):
        """Update regex patterns from configuration"""
        self.account_patterns = {}

        for account_type, config in self.pattern_config.items():
            if config.get("enabled", False) and config.get("patterns"):
                self.account_patterns[account_type] = create_regex_from_patterns(config["patterns"])

        logger.info(f"Loaded patterns for account types: {list(self.account_patterns.keys())}")

    def reload_pattern_config(self):
        """Reload pattern configuration from file"""
        self.pattern_config = load_pattern_config()
        self.update_patterns()

    def get_gpu_status(self) -> dict:
        """Get GPU status information for all engines"""
        status = {
            'engines': {},
            'overall_gpu_available': False,
            'recommended_engine': 'Tesseract'
        }

        for engine_name, engine in self.engines.items():
            if hasattr(engine, 'get_gpu_info'):
                engine_info = engine.get_gpu_info()
                status['engines'][engine_name] = engine_info

                if engine_info.get('using_gpu', False):
                    status['overall_gpu_available'] = True
                    status['recommended_engine'] = engine_name

        return status

    def log_gpu_status(self):
        """Log current GPU status for debugging"""
        status = self.get_gpu_status()

        logger.info("=== OCR GPU Status ===")
        for engine_name, engine_info in status['engines'].items():
            device = engine_info.get('device', 'Unknown')
            logger.info(f"{engine_name}: {device}")

            if engine_info.get('using_gpu', False):
                gpu_name = engine_info.get('gpu_name', 'Unknown GPU')
                memory_total = engine_info.get('gpu_memory_total', 'Unknown')
                logger.info(f"  GPU: {gpu_name} ({memory_total})")

        if status['overall_gpu_available']:
            logger.info(f"✅ GPU acceleration active (recommended: {status['recommended_engine']})")
        else:
            logger.info("⚠️ Using CPU only - consider installing CUDA for faster processing")
        logger.info("=====================")

    def get_enabled_account_types(self) -> List[str]:
        """Get list of enabled account types"""
        return list(self.account_patterns.keys())

    def get_pattern_config(self) -> Dict:
        """Get current pattern configuration"""
        return self.pattern_config.copy()

    def update_pattern_config(self, new_config: Dict):
        """Update pattern configuration"""
        self.pattern_config = new_config
        self.update_patterns()
        self._save_pattern_config()

    def update_account_patterns(self, account_type: str, pattern_list: List[str]):
        """Update patterns for a specific account type"""
        try:
            # Convert pattern list to the format expected by the pattern system
            formatted_patterns = []
            for pattern in pattern_list:
                # Add {code} placeholder if not present
                if '{code}' not in pattern:
                    # Try to intelligently add the placeholder
                    if 'CODE:' in pattern.upper():
                        # Replace "CODE:" with "CODE: {code}"
                        formatted_pattern = re.sub(r'CODE:\s*', 'CODE: {code}', pattern, flags=re.IGNORECASE)
                    else:
                        # Add "USE CODE: {code}" to the end
                        formatted_pattern = f"{pattern} USE CODE: {{code}}"
                else:
                    formatted_pattern = pattern
                formatted_patterns.append(formatted_pattern)

            # Update the pattern configuration
            if account_type not in self.pattern_config:
                self.pattern_config[account_type] = {"enabled": True, "patterns": []}

            self.pattern_config[account_type]["patterns"] = formatted_patterns
            self.pattern_config[account_type]["enabled"] = True

            # Refresh the patterns
            self.update_patterns()

            # Save to file
            self._save_pattern_config()

            logger.info(f"Updated patterns for {account_type}: {formatted_patterns}")

        except Exception as e:
            logger.error(f"Error updating account patterns for {account_type}: {e}")

    def _save_pattern_config(self):
        """Save current pattern configuration to file"""
        try:
            with open("pattern_config.json", 'w', encoding='utf-8') as f:
                json.dump(self.pattern_config, f, indent=2, ensure_ascii=False)
            logger.info("Pattern configuration saved to pattern_config.json")
        except Exception as e:
            logger.error(f"Error saving pattern config: {e}")

    def get_available_engines(self) -> List[str]:
        """Get list of available OCR engines"""
        return [name for name, engine in self.engines.items() if engine.is_available()]
    
    def set_engine(self, engine_name: str) -> bool:
        """Set the active OCR engine"""
        if engine_name in self.engines and self.engines[engine_name].is_available():
            self.current_engine = engine_name
            logger.info(f"Switched to {engine_name} OCR engine")
            return True
        else:
            logger.error(f"OCR engine {engine_name} not available")
            return False
    
    def crop_region_of_interest(self, image: np.ndarray, region=None) -> np.ndarray:
        """Always crop to bottom half of image for OCR scanning"""
        height, width = image.shape[:2]
        print(f"🖼️  Cropping image: {width}x{height} -> bottom half")

        # Always use bottom half of the image (ignore region parameter)
        start_y = int(height * 0.5)  # Start at 50% from top
        cropped = image[start_y:height, 0:width]
        print(f"   Using bottom half: y={start_y}-{height}, x=0-{width}")

        print(f"✂️  Cropped to: {cropped.shape[1]}x{cropped.shape[0]} (from {width}x{height})")
        return cropped
    
    def extract_text_from_image(self, image: np.ndarray, region: str = "bottom_third", use_raw_image: bool = True) -> List[Dict[str, Any]]:
        """Extract text from image using current OCR engine"""
        # Crop to region of interest
        cropped_image = self.crop_region_of_interest(image, region)

        # Get current engine
        engine = self.engines.get(self.current_engine)
        if not engine or not engine.is_available():
            logger.error(f"Current OCR engine {self.current_engine} not available")
            return []

        # Extract text with raw image option
        if hasattr(engine, 'extract_text') and 'use_raw_image' in engine.extract_text.__code__.co_varnames:
            results = engine.extract_text(cropped_image, use_raw_image=use_raw_image)
        else:
            results = engine.extract_text(cropped_image)

        # Use lower confidence threshold since we're using raw images
        min_confidence = 0.3 if use_raw_image else self.min_confidence
        filtered_results = [
            result for result in results
            if result.get('confidence', 0) >= min_confidence
        ]

        return filtered_results
    
    def detect_account_type(self, text: str) -> Optional[str]:
        """
        Step 1: Detect account type from text
        Looks for keywords that indicate the account type
        """
        text_upper = text.upper()

        print(f"🔍 Step 1: Detecting account type in: '{text}'")

        # Define account type keywords (order matters - more specific first)
        account_type_keywords = {
            "Expert": ["EXPERT"],
            "Starter Plus": ["STARTER+", "STARTER PLUS"],
            "Starter": ["STARTER"],  # Must come after "Starter Plus" to avoid false matches
            "Free Reset Code": ["FREE RESET", "RESET", "RESETS"]
        }

        # Check each account type
        for account_type, keywords in account_type_keywords.items():
            for keyword in keywords:
                if keyword in text_upper:
                    print(f"   ✅ Found account type: {account_type} (keyword: '{keyword}')")
                    return account_type

        print(f"   ❌ No account type detected")
        return None

    def extract_code_after_keyword(self, text: str) -> Optional[str]:
        """
        Step 2: Extract code that appears after "CODE:" or account type keywords
        Focuses only on finding the code, ignoring everything before the keyword
        """
        print(f"🔍 Step 2: Extracting code from: '{text}'")

        # Method 1: Look for "CODE:" followed by the actual code (preferred)
        code_pattern = re.compile(r'CODE:\s*([A-Z0-9\-_]+)', re.IGNORECASE)
        match = code_pattern.search(text)
        if match:
            code = match.group(1).strip()
            print(f"   ✅ Found code after 'CODE:': '{code}'")

            # Apply S/5 confusion correction
            corrected_code = self.correct_s5_confusion(code)
            if corrected_code != code:
                print(f"   🔧 S/5 correction applied: '{code}' → '{corrected_code}'")
                code = corrected_code

            return code

        # Method 2: Look for account type keywords followed by colon and code
        # This handles formats like "STARTER: ABC123", "EXPERT: XYZ789"
        account_type_patterns = [
            r'EXPERT:\s*([A-Z0-9\-_]+)',
            r'STARTER\+:\s*([A-Z0-9\-_]+)',
            r'STARTER\s+PLUS:\s*([A-Z0-9\-_]+)',
            r'STARTER:\s*([A-Z0-9\-_]+)',
            r'RESET:\s*([A-Z0-9\-_]+)',
            r'RESETS:\s*([A-Z0-9\-_]+)'
        ]

        for pattern_str in account_type_patterns:
            pattern = re.compile(pattern_str, re.IGNORECASE)
            match = pattern.search(text)
            if match:
                code = match.group(1).strip()
                print(f"   ✅ Found code after account type keyword: '{code}'")

                # Apply S/5 confusion correction
                corrected_code = self.correct_s5_confusion(code)
                if corrected_code != code:
                    print(f"   🔧 S/5 correction applied: '{code}' → '{corrected_code}'")
                    code = corrected_code

                return code

        print(f"   ❌ No code found after 'CODE:' or account type keywords")
        return None

    def correct_s5_confusion(self, code: str) -> str:
        """
        Correct common S/5 OCR confusion in codes using advanced context analysis
        Based on context and common patterns in MyFundedFutures codes
        """
        if not code:
            return code

        print(f"🔧 Checking S/5 confusion in code: '{code}'")

        # Import smart features for advanced analysis
        try:
            from smart_features import CharacterConfusionAnalyzer
            analyzer = CharacterConfusionAnalyzer()
        except ImportError:
            analyzer = None
            print("   ⚠️ Smart features not available, using basic S/5 correction")

        corrected_code = list(code.upper())
        corrections_made = []

        # Analyze each character position
        for i, char in enumerate(corrected_code):
            if char in ['S', '5']:
                if analyzer:
                    # Use advanced context analysis
                    suggested_char = analyzer.analyze_s5_context(''.join(corrected_code), i)
                    if suggested_char != char:
                        corrections_made.append(f"pos {i}: '{char}' → '{suggested_char}'")
                        corrected_code[i] = suggested_char
                else:
                    # Use basic pattern matching
                    corrected_char = self._basic_s5_correction(code, i, char)
                    if corrected_char != char:
                        corrections_made.append(f"pos {i}: '{char}' → '{corrected_char}'")
                        corrected_code[i] = corrected_char

        final_code = ''.join(corrected_code)

        if corrections_made:
            print(f"   ✅ S/5 corrections applied: {', '.join(corrections_made)}")
            print(f"   📝 Final result: '{code}' → '{final_code}'")
        else:
            print(f"   ℹ️ No S/5 correction needed for: '{code}'")

        return final_code

    def _basic_s5_correction(self, code: str, position: int, char: str) -> str:
        """Basic S/5 correction using pattern matching"""
        # Get context around the character
        before = code[:position].upper()
        after = code[position+1:].upper()

        # Common word patterns where S is more likely
        s_patterns = [
            (before.endswith('RE') and after.startswith('ET')),     # RESET
            (before.endswith('PLU') and not after),                # PLUS
            (before == '' and after.startswith('TART')),           # START
            (before == '' and after.startswith('AVE')),            # SAVE
            (before == '' and after.startswith('PECIAL')),         # SPECIAL
            (before.endswith('EXPERT') and not after),             # EXPERTS
            (before.endswith('CODE') and not after),               # CODES
        ]

        # Numeric patterns where 5 is more likely
        five_patterns = [
            (before and before[-1].isdigit()),                     # Number before
            (after and after[0].isdigit()),                        # Number after
            (before and before[-1].isdigit() and after and after[0].isdigit()),  # Between numbers
        ]

        if any(s_patterns):
            return 'S'
        elif any(five_patterns):
            return '5'
        else:
            return char  # Keep original if no clear pattern

    def find_giveaway_codes(self, text_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Find giveaway codes using improved two-step logic:
        1. First detect account type from keywords
        2. Then extract code after "CODE:" keyword
        """
        found_codes = []

        print(f"🎯 Starting improved code detection on {len(text_results)} text results")

        for i, result in enumerate(text_results):
            text = result.get('text', '').strip()
            if not text:
                continue

            print(f"\n--- Processing text result {i+1}: '{text}' ---")

            # Step 1: Detect account type
            account_type = self.detect_account_type(text)

            # Step 2: Extract code after "CODE:"
            code = self.extract_code_after_keyword(text)

            # If we found both account type and code, create a match
            if account_type and code:
                found_code = {
                    'code': code,
                    'type': account_type,
                    'full_text': text,
                    'confidence': result.get('confidence', 0),
                    'bbox': result.get('bbox', [])
                }

                found_codes.append(found_code)
                print(f"🎉 SUCCESS: Found {account_type} code '{code}'")

            elif account_type and not code:
                print(f"⚠️  Found account type '{account_type}' but no code after 'CODE:'")

            elif not account_type and code:
                print(f"⚠️  Found code '{code}' but no account type detected")

            else:
                print(f"❌ No account type or code found in this text")

        print(f"\n🎯 Code detection complete: Found {len(found_codes)} valid codes")
        return found_codes

    def enhance_s5_detection(self, text_results: List[Dict[str, Any]], image: np.ndarray, region: str) -> List[Dict[str, Any]]:
        """
        Perform additional OCR analysis specifically for S/5 confusion detection
        Uses different preprocessing techniques to catch missed characters
        """
        enhanced_results = []

        try:
            import cv2

            # Get the cropped region for focused analysis
            cropped_image = self.crop_region_of_interest(image, region)

            # Apply different preprocessing techniques for S/5 detection
            preprocessing_methods = [
                self._preprocess_for_s_detection,
                self._preprocess_for_5_detection,
                self._preprocess_contrast_enhanced
            ]

            for i, preprocess_func in enumerate(preprocessing_methods):
                try:
                    processed_image = preprocess_func(cropped_image)

                    # Run OCR on preprocessed image
                    engine = self.engines.get(self.current_engine)
                    if engine and engine.is_available():
                        results = engine.extract_text(processed_image)

                        # Filter for results that might contain S/5 confusion
                        for result in results:
                            text = result.get('text', '').strip()
                            if text and ('S' in text.upper() or '5' in text):
                                # Check if this is a new/different result
                                is_new = True
                                for existing in text_results:
                                    if abs(result.get('confidence', 0) - existing.get('confidence', 0)) < 0.1:
                                        if self._text_similarity(text, existing.get('text', '')) > 0.8:
                                            is_new = False
                                            break

                                if is_new:
                                    result['preprocessing_method'] = f"s5_enhanced_{i}"
                                    enhanced_results.append(result)
                                    print(f"   🔧 S/5 enhanced detection: '{text}' (method {i})")

                except Exception as e:
                    print(f"   ⚠️ S/5 enhancement method {i} failed: {e}")
                    continue

        except Exception as e:
            print(f"⚠️ S/5 enhancement failed: {e}")

        return enhanced_results

    def enhance_s5_detection_with_easyocr(self, text_results: List[Dict[str, Any]], image: np.ndarray, region: str) -> List[Dict[str, Any]]:
        """
        Enhanced S/5 detection using multiple EasyOCR configurations
        Uses EasyOCR's built-in parameters optimized for character confusion
        """
        enhanced_results = []

        try:
            # Get the cropped region for focused analysis
            cropped_image = self.crop_region_of_interest(image, region)

            # Get current EasyOCR engine
            engine = self.engines.get('EasyOCR')
            if not engine or not engine.is_available():
                print("   ⚠️ EasyOCR not available for enhanced S/5 detection")
                return enhanced_results

            # Configuration 1: Optimized for S detection (curved characters)
            s_optimized_config = {
                'detail': 1,
                'paragraph': True,  # Enable paragraph mode for better context
                'width_ths': 0.4,  # Lower for better word separation
                'height_ths': 0.4,
                'decoder': 'beamsearch',
                'beamWidth': 20,  # Higher beam width for more alternatives
                'batch_size': 1,
                # Remove allowlist to capture spaces and special characters
                'text_threshold': 0.3,  # Lower threshold to catch more text
                'link_threshold': 0.1,  # Lower for better word grouping
                'low_text': 0.1,
                'canvas_size': 4096,  # Higher resolution
                'mag_ratio': 2.5  # Higher magnification
            }

            # Configuration 2: Optimized for 5 detection (angular characters)
            five_optimized_config = {
                'detail': 1,
                'paragraph': True,  # Enable paragraph mode for better context
                'width_ths': 0.5,
                'height_ths': 0.5,
                'decoder': 'beamsearch',  # Use beamsearch for better accuracy
                'beamWidth': 15,
                'batch_size': 1,
                # Remove allowlist to capture spaces and special characters
                'text_threshold': 0.4,  # Lower threshold for better detection
                'link_threshold': 0.2,
                'low_text': 0.2,
                'canvas_size': 3840,
                'mag_ratio': 2.0
            }

            # Configuration 3: High precision mode for complete text
            precision_config = {
                'detail': 1,
                'paragraph': True,  # Enable paragraph mode for complete sentences
                'width_ths': 0.3,  # Lower threshold for better word capture
                'height_ths': 0.3,
                'decoder': 'beamsearch',
                'beamWidth': 25,  # Maximum beam width
                'batch_size': 1,
                # Remove allowlist to capture complete text with spaces and punctuation
                'text_threshold': 0.2,  # Lower threshold for complete text
                'link_threshold': 0.1,  # Lower for better word grouping
                'low_text': 0.1,
                'canvas_size': 5120,  # Maximum resolution
                'mag_ratio': 3.0  # Maximum magnification
            }

            configs = [
                ("S-optimized", s_optimized_config),
                ("5-optimized", five_optimized_config),
                ("High-precision", precision_config)
            ]

            for config_name, config in configs:
                try:
                    print(f"   🔧 Running {config_name} EasyOCR configuration...")

                    # Run EasyOCR with specialized configuration
                    results = engine.model.readtext(cropped_image, **config)

                    # Process results
                    for result in results:
                        try:
                            # Handle different result formats from EasyOCR
                            if len(result) == 3:
                                bbox, text, confidence = result
                            elif len(result) == 2:
                                bbox, text = result
                                confidence = 0.0  # Mark as unknown confidence when not provided
                                print(f"   ⚠️ No confidence provided for {config_name} text: '{text.strip()}'")
                            else:
                                print(f"   ⚠️ Unexpected result format in {config_name}: {result}")
                                continue

                            if confidence >= 0.3 and text.strip():  # Lower threshold for enhanced detection
                                text = text.strip()

                                # Check if this contains S or 5 and might be different from existing results
                                if ('S' in text.upper() or '5' in text) and self._is_new_s5_result(text, text_results, enhanced_results):
                                    enhanced_result = {
                                        'text': text,
                                        'confidence': confidence,
                                        'bbox': bbox,
                                        'enhancement_method': f"easyocr_{config_name.lower().replace('-', '_')}",
                                        'original_detection': False
                                    }
                                    enhanced_results.append(enhanced_result)
                                    print(f"   ✅ {config_name} found: '{text}' (confidence: {confidence:.3f})")
                        except Exception as e:
                            print(f"   ⚠️ Error processing {config_name} result {result}: {e}")
                            continue

                except Exception as e:
                    print(f"   ⚠️ {config_name} configuration failed: {e}")
                    continue

        except Exception as e:
            print(f"⚠️ Enhanced EasyOCR S/5 detection failed: {e}")

        return enhanced_results

    def _is_new_s5_result(self, new_text: str, original_results: List[Dict], enhanced_results: List[Dict]) -> bool:
        """Check if this is a genuinely new S/5 result worth considering"""
        new_text_upper = new_text.upper()

        # Check against original results
        for result in original_results:
            existing_text = result.get('text', '').upper()
            if self._text_similarity(new_text_upper, existing_text) > 0.7:
                # If texts are very similar, only consider it new if it has different S/5 characters
                if self._has_different_s5_chars(new_text_upper, existing_text):
                    return True
                else:
                    return False

        # Check against other enhanced results
        for result in enhanced_results:
            existing_text = result.get('text', '').upper()
            if self._text_similarity(new_text_upper, existing_text) > 0.8:
                return False

        return True

    def _has_different_s5_chars(self, text1: str, text2: str) -> bool:
        """Check if two texts have different S/5 characters in the same positions"""
        if len(text1) != len(text2):
            return True

        for i, (c1, c2) in enumerate(zip(text1, text2)):
            if c1 in ['S', '5'] and c2 in ['S', '5'] and c1 != c2:
                return True

        return False

    def _preprocess_for_s_detection(self, image: np.ndarray) -> np.ndarray:
        """Preprocessing optimized for detecting S characters"""
        import cv2

        # Convert to grayscale
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()

        # Apply morphological operations to enhance S-like curves
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (2, 3))
        processed = cv2.morphologyEx(gray, cv2.MORPH_CLOSE, kernel)

        # Enhance contrast for curved characters
        processed = cv2.equalizeHist(processed)

        return processed

    def _preprocess_for_5_detection(self, image: np.ndarray) -> np.ndarray:
        """Preprocessing optimized for detecting 5 characters"""
        import cv2

        # Convert to grayscale
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()

        # Apply morphological operations to enhance angular features
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
        processed = cv2.morphologyEx(gray, cv2.MORPH_OPEN, kernel)

        # Sharpen edges for angular characters
        kernel_sharp = np.array([[-1,-1,-1], [-1,9,-1], [-1,-1,-1]])
        processed = cv2.filter2D(processed, -1, kernel_sharp)

        return processed

    def _preprocess_contrast_enhanced(self, image: np.ndarray) -> np.ndarray:
        """High contrast preprocessing for better character distinction"""
        import cv2

        # Convert to grayscale
        if len(image.shape) == 3:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        else:
            gray = image.copy()

        # Apply CLAHE (Contrast Limited Adaptive Histogram Equalization)
        clahe = cv2.createCLAHE(clipLimit=3.0, tileGridSize=(8,8))
        processed = clahe.apply(gray)

        # Apply bilateral filter to reduce noise while preserving edges
        processed = cv2.bilateralFilter(processed, 9, 75, 75)

        return processed

    def _text_similarity(self, text1: str, text2: str) -> float:
        """Calculate similarity between two text strings"""
        if not text1 or not text2:
            return 0.0

        # Simple character-based similarity
        text1, text2 = text1.upper(), text2.upper()
        if text1 == text2:
            return 1.0

        # Calculate character overlap
        common_chars = sum(1 for c1, c2 in zip(text1, text2) if c1 == c2)
        max_length = max(len(text1), len(text2))

        return common_chars / max_length if max_length > 0 else 0.0
    
    def process_frame_for_codes(self, image: np.ndarray, region: str = "bottom_third") -> List[Dict[str, Any]]:
        """Complete pipeline: extract text and find codes in one call"""
        print(f"🔍 OCR Manager: Processing frame for codes (RAW IMAGE MODE)")
        print(f"   Image shape: {image.shape}")
        print(f"   Region: {region}")
        print(f"   Current engine: {self.current_engine}")

        # Extract text from image using raw image processing
        text_results = self.extract_text_from_image(image, region, use_raw_image=True)
        print(f"📝 OCR extracted {len(text_results)} text results")

        # Perform additional S/5 confusion check if codes are found
        if text_results:
            enhanced_results = self.enhance_s5_detection_with_easyocr(text_results, image, region)
            if enhanced_results:
                print(f"🔧 Enhanced S/5 detection found {len(enhanced_results)} additional results")
                text_results.extend(enhanced_results)

        if text_results:
            for i, result in enumerate(text_results):
                print(f"   Text {i+1}: '{result.get('text', '')}' (confidence: {result.get('confidence', 0):.2f})")
        else:
            print("   No text detected by OCR")

        # Find codes in the extracted text
        codes = self.find_giveaway_codes(text_results)
        print(f"🎯 Found {len(codes)} codes after pattern matching")

        if codes:
            for i, code in enumerate(codes):
                print(f"   Code {i+1}: {code}")

        return codes


# Global OCR manager instance
ocr_manager = OCRManager()
