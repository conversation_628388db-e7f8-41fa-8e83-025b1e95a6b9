"""
Live Stream Testing GUI for MFFUHijack
Main interface for testing previous livestreams with configurable intervals
"""

import sys
import os
import time
import threading
from datetime import datetime
from typing import Dict, List, Optional
from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *

from livestream_testing_mode import (
    CodeValidationTester, StreamProcessor, YouTubeStreamDownloader, TestingStatistics
)

# Import manual OCR region selector
try:
    from manual_ocr_region_selector import OCRRegionSelectorDialog
    OCR_REGION_SELECTOR_AVAILABLE = True
except ImportError as e:
    print(f"OCR region selector not available: {e}")
    OCR_REGION_SELECTOR_AVAILABLE = False

# Try to import OCR engines
try:
    import easyocr
    EASYOCR_AVAILABLE = True
except ImportError:
    EASYOCR_AVAILABLE = False

try:
    import paddleocr
    PADDLEOCR_AVAILABLE = True
except ImportError:
    PADDLEOCR_AVAILABLE = False


class TestingWorker(QThread):
    """Worker thread for stream testing"""
    
    # Signals
    progress_updated = pyqtSignal(int)  # Progress percentage
    frame_processed = pyqtSignal(str, dict)  # Timestamp, frame info
    code_detected = pyqtSignal(dict)  # Code data
    validation_completed = pyqtSignal(dict)  # Validation result
    testing_finished = pyqtSignal(dict)  # Final statistics
    error_occurred = pyqtSignal(str)  # Error message
    
    def __init__(self):
        super().__init__()
        self.stream_processor = StreamProcessor()
        self.code_validator = CodeValidationTester()
        self.statistics = TestingStatistics()
        
        # Testing parameters
        self.stream_path = ""
        self.interval_seconds = 5.0
        self.ocr_engine = "easyocr"
        self.validate_codes = True
        self.ocr_region = (0, 67, 100, 33)  # x, y, width, height as percentages
        
        # Control flags
        self.is_running = False
        self.should_stop = False
        
        # Initialize OCR
        self.init_ocr()
    
    def init_ocr(self):
        """Initialize OCR engines"""
        try:
            if EASYOCR_AVAILABLE and self.ocr_engine == "easyocr":
                self.ocr_reader = easyocr.Reader(['en'])
                print("✅ EasyOCR initialized")
            elif PADDLEOCR_AVAILABLE and self.ocr_engine == "paddleocr":
                self.ocr_reader = paddleocr.PaddleOCR(use_angle_cls=True, lang='en')
                print("✅ PaddleOCR initialized")
            else:
                self.ocr_reader = None
                print("❌ No OCR engine available")
        except Exception as e:
            self.ocr_reader = None
            print(f"❌ OCR initialization failed: {e}")
    
    def set_testing_parameters(self, stream_path: str, interval: float, engine: str, 
                             validate: bool, region: tuple):
        """Set testing parameters"""
        self.stream_path = stream_path
        self.interval_seconds = interval
        self.ocr_engine = engine
        self.validate_codes = validate
        self.ocr_region = region
        
        # Reinitialize OCR if engine changed
        if hasattr(self, 'ocr_reader'):
            self.init_ocr()
    
    def run(self):
        """Main testing loop"""
        try:
            self.is_running = True
            self.should_stop = False
            self.statistics.reset()
            
            # Load stream
            if not self.stream_processor.load_stream(self.stream_path):
                self.error_occurred.emit("Failed to load stream")
                return
            
            print(f"🚀 Starting testing with {self.interval_seconds}s intervals")
            
            # Process frames at intervals
            while self.is_running and not self.should_stop:
                start_time = time.time()
                
                # Get next frame
                frame_result = self.stream_processor.get_frame_at_interval(self.interval_seconds)
                
                if frame_result is None:
                    break  # End of stream
                
                ret, frame = frame_result
                if not ret:
                    break
                
                # Process frame
                timestamp = self.stream_processor.get_current_timestamp()
                progress = self.stream_processor.get_progress_percentage()
                
                # Extract OCR region
                ocr_frame = self.extract_ocr_region(frame)
                
                # Perform OCR
                detected_codes = self.perform_ocr(ocr_frame)
                
                # Process detected codes
                for code_data in detected_codes:
                    self.statistics.add_code_detected(code_data)
                    self.code_detected.emit(code_data)
                    
                    # Validate code if enabled
                    if self.validate_codes and code_data.get('code'):
                        validation_result = self.code_validator.validate_code(
                            code_data['code'], 
                            code_data.get('type', 'Starter')
                        )
                        self.statistics.add_validation_result(validation_result)
                        self.validation_completed.emit(validation_result)
                
                # Update statistics and progress
                processing_time = time.time() - start_time
                self.statistics.add_frame_processed(processing_time)
                
                frame_info = {
                    'timestamp': timestamp,
                    'progress': progress,
                    'codes_found': len(detected_codes),
                    'processing_time': processing_time
                }
                
                self.frame_processed.emit(timestamp, frame_info)
                self.progress_updated.emit(int(progress))
                
                # Small delay to prevent overwhelming
                self.msleep(100)
            
            # Emit final statistics
            final_stats = self.statistics.get_comprehensive_stats()
            self.testing_finished.emit(final_stats)
            
        except Exception as e:
            self.error_occurred.emit(f"Testing error: {str(e)}")
        finally:
            self.is_running = False
            self.stream_processor.close()
    
    def extract_ocr_region(self, frame):
        """Extract OCR region from frame"""
        if frame is None:
            return None
        
        height, width = frame.shape[:2]
        
        # Calculate region coordinates
        x = int(width * self.ocr_region[0] / 100)
        y = int(height * self.ocr_region[1] / 100)
        w = int(width * self.ocr_region[2] / 100)
        h = int(height * self.ocr_region[3] / 100)
        
        # Extract region
        ocr_frame = frame[y:y+h, x:x+w]
        return ocr_frame
    
    def perform_ocr(self, frame) -> List[Dict]:
        """Perform OCR on frame and extract codes"""
        if frame is None or self.ocr_reader is None:
            return []
        
        try:
            detected_codes = []
            
            if self.ocr_engine == "easyocr":
                results = self.ocr_reader.readtext(frame)
                
                for (bbox, text, confidence) in results:
                    if confidence > 0.5:  # Minimum confidence threshold
                        code_data = self.extract_code_from_text(text, confidence)
                        if code_data:
                            detected_codes.append(code_data)
            
            elif self.ocr_engine == "paddleocr":
                results = self.ocr_reader.ocr(frame, cls=True)
                
                if results and results[0]:
                    for line in results[0]:
                        if len(line) >= 2:
                            text = line[1][0]
                            confidence = line[1][1]
                            
                            if confidence > 0.5:
                                code_data = self.extract_code_from_text(text, confidence)
                                if code_data:
                                    detected_codes.append(code_data)
            
            return detected_codes
            
        except Exception as e:
            print(f"OCR error: {e}")
            return []
    
    def extract_code_from_text(self, text: str, confidence: float) -> Optional[Dict]:
        """Extract code information from OCR text"""
        import re
        
        # Look for code patterns - prioritize "CODE:" pattern
        code_patterns = [
            r'CODE:\s*([A-Z0-9]{3,10})',  # PRIMARY: CODE: XXXXX (highest priority)
            r'USE\s+CODE\s*:?\s*([A-Z0-9]{3,10})',  # SECONDARY: USE CODE XXXXX
            r'(?:RESET|STARTER|EXPERT|PLUS):\s*([A-Z0-9]{3,10})',  # TERTIARY: Account type with colon
            r'([A-Z0-9]{5,8})',  # FALLBACK: Direct alphanumeric codes (last resort)
        ]
        
        # Account type detection
        account_types = {
            'RESET': 'Reset',
            'FREE RESET': 'Free Reset Code',
            'STARTER': 'Starter',
            'START': 'Starter',
            'PLUS': 'Starter Plus',
            'EXPERT': 'Expert'
        }
        
        text_upper = text.upper()
        
        # Find code - prioritize "CODE:" pattern
        code = None
        pattern_used = None
        for i, pattern in enumerate(code_patterns):
            match = re.search(pattern, text_upper)
            if match:
                code = match.group(1) if len(match.groups()) > 0 else match.group(0)
                pattern_names = ["CODE:", "USE CODE", "ACCOUNT TYPE:", "FALLBACK"]
                pattern_used = pattern_names[i]
                print(f"   ✅ Found code using {pattern_used} pattern: '{code}'")

                # If we found code using high-priority patterns, stop searching
                if i <= 1:  # CODE: or USE CODE patterns
                    print(f"   🎯 Using high-priority pattern, stopping search")
                    break
                break
        
        if not code:
            return None
        
        # Determine account type
        account_type = "Starter"  # Default
        for keyword, acc_type in account_types.items():
            if keyword in text_upper:
                account_type = acc_type
                break
        
        return {
            'code': code,
            'type': account_type,
            'confidence': confidence,
            'full_text': text,
            'timestamp': datetime.now().isoformat()
        }
    
    def stop_testing(self):
        """Stop the testing process"""
        self.should_stop = True
        self.is_running = False


class LiveStreamTestingWindow(QMainWindow):
    """Main window for livestream testing"""
    
    def __init__(self):
        super().__init__()
        self.testing_worker = None
        self.downloader = YouTubeStreamDownloader()
        self.init_ui()
        
        # Load available streams
        self.refresh_stream_list()
    
    def init_ui(self):
        """Initialize the user interface"""
        self.setWindowTitle("MFFUHijack - Live Stream Testing Mode")
        self.setMinimumSize(1200, 800)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Main layout
        main_layout = QVBoxLayout(central_widget)
        
        # Header
        header_layout = QHBoxLayout()
        
        title_label = QLabel("🧪 Live Stream Testing Mode")
        title_label.setFont(QFont("Arial", 18, QFont.Weight.Bold))
        header_layout.addWidget(title_label)
        
        header_layout.addStretch()
        
        # Status indicator
        self.status_label = QLabel("⚫ Ready")
        self.status_label.setFont(QFont("Arial", 12, QFont.Weight.Bold))
        header_layout.addWidget(self.status_label)
        
        main_layout.addLayout(header_layout)
        
        # Create main content
        content_splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Left panel - Configuration
        left_panel = self.create_configuration_panel()
        content_splitter.addWidget(left_panel)
        
        # Right panel - Results
        right_panel = self.create_results_panel()
        content_splitter.addWidget(right_panel)
        
        content_splitter.setSizes([500, 700])

        # Connect splitter resize to autosave
        self.content_splitter = content_splitter
        content_splitter.splitterMoved.connect(self.on_splitter_moved)

        main_layout.addWidget(content_splitter)
        
        # Progress bar
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        main_layout.addWidget(self.progress_bar)
    
    def create_configuration_panel(self):
        """Create configuration panel"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Stream Selection
        stream_group = QGroupBox("📺 Stream Selection")
        stream_layout = QVBoxLayout(stream_group)
        
        # URL input
        url_layout = QHBoxLayout()
        url_layout.addWidget(QLabel("YouTube URL:"))
        self.url_input = QLineEdit()
        self.url_input.setPlaceholderText("https://www.youtube.com/watch?v=...")
        url_layout.addWidget(self.url_input)
        
        download_btn = QPushButton("📥 Download")
        download_btn.clicked.connect(self.download_stream)
        url_layout.addWidget(download_btn)
        
        stream_layout.addLayout(url_layout)
        
        # Available streams
        stream_layout.addWidget(QLabel("Available Streams:"))
        self.stream_list = QListWidget()
        self.stream_list.setMaximumHeight(150)
        stream_layout.addWidget(self.stream_list)
        
        refresh_btn = QPushButton("🔄 Refresh List")
        refresh_btn.clicked.connect(self.refresh_stream_list)
        stream_layout.addWidget(refresh_btn)
        
        layout.addWidget(stream_group)
        
        # Testing Configuration
        config_group = QGroupBox("⚙️ Testing Configuration")
        config_layout = QGridLayout(config_group)
        
        # Frame interval
        config_layout.addWidget(QLabel("Frame Interval:"), 0, 0)
        self.interval_spin = QDoubleSpinBox()
        self.interval_spin.setRange(1.0, 60.0)
        self.interval_spin.setValue(5.0)
        self.interval_spin.setSuffix(" seconds")
        config_layout.addWidget(self.interval_spin, 0, 1)
        
        # OCR Engine
        config_layout.addWidget(QLabel("OCR Engine:"), 1, 0)
        self.ocr_combo = QComboBox()
        if EASYOCR_AVAILABLE:
            self.ocr_combo.addItem("EasyOCR", "easyocr")
        if PADDLEOCR_AVAILABLE:
            self.ocr_combo.addItem("PaddleOCR", "paddleocr")
        if self.ocr_combo.count() == 0:
            self.ocr_combo.addItem("No OCR Available", "none")
        config_layout.addWidget(self.ocr_combo, 1, 1)
        
        # Code validation
        self.validate_checkbox = QCheckBox("Validate codes (test without buying)")
        self.validate_checkbox.setChecked(True)
        config_layout.addWidget(self.validate_checkbox, 2, 0, 1, 2)
        
        layout.addWidget(config_group)
        
        # OCR Region Configuration
        region_group = QGroupBox("🎯 OCR Region Selection")
        region_layout = QVBoxLayout(region_group)

        # Current region display
        self.region_display_label = QLabel("Current Region: 0%, 67%, 100%, 33% (X, Y, Width, Height)")
        self.region_display_label.setStyleSheet("font-family: monospace; background-color: #f0f0f0; padding: 5px; border-radius: 3px;")
        region_layout.addWidget(self.region_display_label)

        # Manual selector button
        if OCR_REGION_SELECTOR_AVAILABLE:
            select_region_btn = QPushButton("🎯 Manual OCR Region Selection")
            select_region_btn.clicked.connect(self.open_ocr_region_selector)
            select_region_btn.setStyleSheet("""
                QPushButton {
                    font-weight: bold;
                    background-color: #2196F3;
                    color: white;
                    border: none;
                    border-radius: 5px;
                    padding: 8px;
                }
                QPushButton:hover {
                    background-color: #1976D2;
                }
            """)
            region_layout.addWidget(select_region_btn)
        else:
            # Fallback to spin boxes if manual selector not available
            fallback_layout = QGridLayout()

            fallback_layout.addWidget(QLabel("X Position (%):"), 0, 0)
            self.region_x_spin = QSpinBox()
            self.region_x_spin.setRange(0, 100)
            self.region_x_spin.setValue(0)
            self.region_x_spin.valueChanged.connect(self.update_region_display)
            fallback_layout.addWidget(self.region_x_spin, 0, 1)

            fallback_layout.addWidget(QLabel("Y Position (%):"), 0, 2)
            self.region_y_spin = QSpinBox()
            self.region_y_spin.setRange(0, 100)
            self.region_y_spin.setValue(67)
            self.region_y_spin.valueChanged.connect(self.update_region_display)
            fallback_layout.addWidget(self.region_y_spin, 0, 3)

            fallback_layout.addWidget(QLabel("Width (%):"), 1, 0)
            self.region_w_spin = QSpinBox()
            self.region_w_spin.setRange(1, 100)
            self.region_w_spin.setValue(100)
            self.region_w_spin.valueChanged.connect(self.update_region_display)
            fallback_layout.addWidget(self.region_w_spin, 1, 1)

            fallback_layout.addWidget(QLabel("Height (%):"), 1, 2)
            self.region_h_spin = QSpinBox()
            self.region_h_spin.setRange(1, 100)
            self.region_h_spin.setValue(33)
            self.region_h_spin.valueChanged.connect(self.update_region_display)
            fallback_layout.addWidget(self.region_h_spin, 1, 3)

            region_layout.addLayout(fallback_layout)

        # Initialize OCR region
        self.current_ocr_region = (0.0, 67.0, 100.0, 33.0)
        self.update_region_display()

        layout.addWidget(region_group)

        # Load saved splitter state after UI is initialized
        QTimer.singleShot(100, self.load_splitter_state)
        
        # Control buttons
        control_layout = QHBoxLayout()
        
        self.start_btn = QPushButton("🚀 Start Testing")
        self.start_btn.clicked.connect(self.start_testing)
        control_layout.addWidget(self.start_btn)
        
        self.stop_btn = QPushButton("⏹️ Stop Testing")
        self.stop_btn.clicked.connect(self.stop_testing)
        self.stop_btn.setEnabled(False)
        control_layout.addWidget(self.stop_btn)
        
        layout.addLayout(control_layout)
        
        layout.addStretch()
        
        return panel

    def create_results_panel(self):
        """Create results panel"""
        panel = QWidget()
        layout = QVBoxLayout(panel)

        # Results tabs
        tab_widget = QTabWidget()

        # Real-time Results Tab
        realtime_tab = QWidget()
        realtime_layout = QVBoxLayout(realtime_tab)

        # Current status
        status_group = QGroupBox("📊 Current Status")
        status_layout = QGridLayout(status_group)

        status_layout.addWidget(QLabel("Current Time:"), 0, 0)
        self.current_time_label = QLabel("00:00:00")
        status_layout.addWidget(self.current_time_label, 0, 1)

        status_layout.addWidget(QLabel("Frames Processed:"), 1, 0)
        self.frames_processed_label = QLabel("0")
        status_layout.addWidget(self.frames_processed_label, 1, 1)

        status_layout.addWidget(QLabel("Codes Detected:"), 2, 0)
        self.codes_detected_label = QLabel("0")
        status_layout.addWidget(self.codes_detected_label, 2, 1)

        status_layout.addWidget(QLabel("Codes Validated:"), 3, 0)
        self.codes_validated_label = QLabel("0")
        status_layout.addWidget(self.codes_validated_label, 3, 1)

        realtime_layout.addWidget(status_group)

        # Detection log
        log_group = QGroupBox("🔍 Detection Log")
        log_layout = QVBoxLayout(log_group)

        self.detection_log = QTextEdit()
        self.detection_log.setMaximumHeight(200)
        self.detection_log.setReadOnly(True)
        log_layout.addWidget(self.detection_log)

        realtime_layout.addWidget(log_group)

        # Validation results
        validation_group = QGroupBox("✅ Validation Results")
        validation_layout = QVBoxLayout(validation_group)

        self.validation_tree = QTreeWidget()
        self.validation_tree.setHeaderLabels(["Code", "Type", "Status", "Message", "Time"])
        self.validation_tree.setMaximumHeight(200)
        validation_layout.addWidget(self.validation_tree)

        realtime_layout.addWidget(validation_group)

        tab_widget.addTab(realtime_tab, "📊 Real-time")

        # Statistics Tab
        stats_tab = QWidget()
        stats_layout = QVBoxLayout(stats_tab)

        # Session statistics
        session_group = QGroupBox("📈 Session Statistics")
        session_layout = QGridLayout(session_group)

        session_layout.addWidget(QLabel("Session Duration:"), 0, 0)
        self.session_duration_label = QLabel("00:00:00")
        session_layout.addWidget(self.session_duration_label, 0, 1)

        session_layout.addWidget(QLabel("Average Processing Time:"), 1, 0)
        self.avg_processing_label = QLabel("0.0 ms")
        session_layout.addWidget(self.avg_processing_label, 1, 1)

        session_layout.addWidget(QLabel("Detection Rate:"), 2, 0)
        self.detection_rate_label = QLabel("0.0 codes/frame")
        session_layout.addWidget(self.detection_rate_label, 2, 1)

        session_layout.addWidget(QLabel("Validation Success Rate:"), 3, 0)
        self.validation_success_label = QLabel("0.0%")
        session_layout.addWidget(self.validation_success_label, 3, 1)

        stats_layout.addWidget(session_group)

        # Detailed statistics
        self.stats_text = QTextEdit()
        self.stats_text.setReadOnly(True)
        stats_layout.addWidget(self.stats_text)

        tab_widget.addTab(stats_tab, "📈 Statistics")

        layout.addWidget(tab_widget)

        return panel

    def refresh_stream_list(self):
        """Refresh the list of available streams"""
        self.stream_list.clear()

        streams = self.downloader.list_downloaded_streams()
        for stream in streams:
            item_text = f"{stream['filename']} ({stream['size_mb']:.1f} MB)"
            item = QListWidgetItem(item_text)
            item.setData(Qt.ItemDataRole.UserRole, stream['path'])
            self.stream_list.addItem(item)

    def download_stream(self):
        """Download a stream from YouTube"""
        url = self.url_input.text().strip()
        if not url:
            QMessageBox.warning(self, "Warning", "Please enter a YouTube URL")
            return

        # Show progress dialog
        progress_dialog = QProgressDialog("Downloading stream...", "Cancel", 0, 0, self)
        progress_dialog.setWindowModality(Qt.WindowModality.WindowModal)
        progress_dialog.show()

        # Download in thread (simplified for demo)
        def download_thread():
            try:
                file_path = self.downloader.download_stream(url)
                if file_path:
                    QTimer.singleShot(0, lambda: self.on_download_complete(file_path, progress_dialog))
                else:
                    QTimer.singleShot(0, lambda: self.on_download_error("Download failed", progress_dialog))
            except Exception as e:
                QTimer.singleShot(0, lambda: self.on_download_error(str(e), progress_dialog))

        import threading
        threading.Thread(target=download_thread, daemon=True).start()

    def on_download_complete(self, file_path: str, dialog: QProgressDialog):
        """Handle download completion"""
        dialog.close()
        QMessageBox.information(self, "Success", f"Stream downloaded successfully!\n{file_path}")
        self.refresh_stream_list()

    def on_download_error(self, error: str, dialog: QProgressDialog):
        """Handle download error"""
        dialog.close()
        QMessageBox.critical(self, "Error", f"Download failed: {error}")

    def start_testing(self):
        """Start the testing process"""
        # Get selected stream
        current_item = self.stream_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "Warning", "Please select a stream to test")
            return

        stream_path = current_item.data(Qt.ItemDataRole.UserRole)

        # Get configuration
        interval = self.interval_spin.value()
        ocr_engine = self.ocr_combo.currentData()
        validate_codes = self.validate_checkbox.isChecked()

        # Get OCR region
        if hasattr(self, 'current_ocr_region'):
            ocr_region = self.current_ocr_region
        elif hasattr(self, 'region_x_spin'):
            # Fallback to spin boxes
            ocr_region = (
                self.region_x_spin.value(),
                self.region_y_spin.value(),
                self.region_w_spin.value(),
                self.region_h_spin.value()
            )
        else:
            # Default region
            ocr_region = (0.0, 67.0, 100.0, 33.0)

        if ocr_engine == "none":
            QMessageBox.warning(self, "Warning", "No OCR engine available")
            return

        # Setup worker
        self.testing_worker = TestingWorker()
        self.testing_worker.set_testing_parameters(
            stream_path, interval, ocr_engine, validate_codes, ocr_region
        )

        # Connect signals
        self.testing_worker.progress_updated.connect(self.update_progress)
        self.testing_worker.frame_processed.connect(self.on_frame_processed)
        self.testing_worker.code_detected.connect(self.on_code_detected)
        self.testing_worker.validation_completed.connect(self.on_validation_completed)
        self.testing_worker.testing_finished.connect(self.on_testing_finished)
        self.testing_worker.error_occurred.connect(self.on_error)

        # Start testing
        self.testing_worker.start()

        # Update UI
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.progress_bar.setVisible(True)
        self.status_label.setText("🟢 Testing")

        # Clear previous results
        self.detection_log.clear()
        self.validation_tree.clear()
        self.stats_text.clear()

    def stop_testing(self):
        """Stop the testing process"""
        if self.testing_worker:
            self.testing_worker.stop_testing()
            self.testing_worker.wait()

        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.progress_bar.setVisible(False)
        self.status_label.setText("⚫ Stopped")

    def update_progress(self, percentage: int):
        """Update progress bar"""
        self.progress_bar.setValue(percentage)

    def on_frame_processed(self, timestamp: str, frame_info: dict):
        """Handle frame processed event"""
        self.current_time_label.setText(timestamp)

        # Update counters (these will be updated by statistics)
        if hasattr(self.testing_worker, 'statistics'):
            stats = self.testing_worker.statistics
            self.frames_processed_label.setText(str(stats.frames_processed))
            self.codes_detected_label.setText(str(stats.codes_detected))
            self.codes_validated_label.setText(str(stats.codes_validated))

    def on_code_detected(self, code_data: dict):
        """Handle code detection event"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        code = code_data.get('code', '')
        code_type = code_data.get('type', '')
        confidence = code_data.get('confidence', 0)

        log_entry = f"[{timestamp}] 🔍 Code detected: {code} ({code_type}) - Confidence: {confidence:.2f}"
        self.detection_log.append(log_entry)

        # Auto-scroll to bottom
        scrollbar = self.detection_log.verticalScrollBar()
        scrollbar.setValue(scrollbar.maximum())

    def on_validation_completed(self, result: dict):
        """Handle validation completion"""
        code = result.get('code', '')
        account_type = result.get('account_type', '')
        valid = result.get('valid', False)
        message = result.get('message', result.get('error', ''))
        timestamp = datetime.now().strftime("%H:%M:%S")

        # Add to validation tree
        item = QTreeWidgetItem([
            code,
            account_type,
            "✅ Valid" if valid else "❌ Invalid",
            message,
            timestamp
        ])

        # Color code the item
        if valid:
            item.setBackground(0, QColor(200, 255, 200))  # Light green
        else:
            item.setBackground(0, QColor(255, 200, 200))  # Light red

        self.validation_tree.addTopLevelItem(item)
        self.validation_tree.scrollToBottom()

    def on_testing_finished(self, final_stats: dict):
        """Handle testing completion"""
        self.status_label.setText("✅ Completed")

        # Update statistics display
        self.session_duration_label.setText(final_stats.get('session_duration', '00:00:00'))
        self.avg_processing_label.setText(f"{final_stats.get('average_processing_time', 0)*1000:.1f} ms")
        self.detection_rate_label.setText(f"{final_stats.get('detection_rate', 0):.3f} codes/frame")
        self.validation_success_label.setText(f"{final_stats.get('validation_success_rate', 0):.1f}%")

        # Show detailed statistics
        stats_text = "🧪 TESTING SESSION COMPLETE\n"
        stats_text += "=" * 50 + "\n\n"

        for key, value in final_stats.items():
            formatted_key = key.replace('_', ' ').title()
            stats_text += f"{formatted_key}: {value}\n"

        stats_text += "\n📊 SUMMARY:\n"
        stats_text += f"• Total frames processed: {final_stats.get('frames_processed', 0)}\n"
        stats_text += f"• Codes detected: {final_stats.get('codes_detected', 0)}\n"
        stats_text += f"• Valid codes found: {final_stats.get('valid_codes', 0)}\n"
        stats_text += f"• Invalid codes: {final_stats.get('invalid_codes', 0)}\n"
        stats_text += f"• Validation errors: {final_stats.get('validation_errors', 0)}\n"

        self.stats_text.setPlainText(stats_text)

        # Show completion message
        QMessageBox.information(
            self,
            "Testing Complete",
            f"Testing completed successfully!\n\n"
            f"Frames processed: {final_stats.get('frames_processed', 0)}\n"
            f"Codes detected: {final_stats.get('codes_detected', 0)}\n"
            f"Valid codes: {final_stats.get('valid_codes', 0)}\n"
            f"Success rate: {final_stats.get('validation_success_rate', 0):.1f}%"
        )

    def on_error(self, error_message: str):
        """Handle error"""
        self.status_label.setText("❌ Error")
        QMessageBox.critical(self, "Error", error_message)
        self.stop_testing()

    def open_ocr_region_selector(self):
        """Open the manual OCR region selector"""
        if not OCR_REGION_SELECTOR_AVAILABLE:
            QMessageBox.warning(self, "Not Available", "Manual OCR region selector is not available")
            return

        try:
            # Open the selector dialog with current region
            dialog = OCRRegionSelectorDialog(self, initial_region=self.current_ocr_region)

            if dialog.exec() == QDialog.DialogCode.Accepted:
                # Get the selected region
                selected_region = dialog.get_selected_region()
                self.current_ocr_region = selected_region

                # Update display
                self.update_region_display()

                QMessageBox.information(self, "OCR Region Updated",
                                      f"OCR region has been updated successfully!\n\n"
                                      f"Position: {selected_region[0]:.1f}%, {selected_region[1]:.1f}%\n"
                                      f"Size: {selected_region[2]:.1f}% × {selected_region[3]:.1f}%")

        except Exception as e:
            QMessageBox.critical(self, "Error", f"Failed to open OCR region selector: {e}")

    def update_region_display(self):
        """Update the region display label"""
        if hasattr(self, 'current_ocr_region'):
            x, y, w, h = self.current_ocr_region
        elif hasattr(self, 'region_x_spin'):
            # Get from spin boxes
            x = self.region_x_spin.value()
            y = self.region_y_spin.value()
            w = self.region_w_spin.value()
            h = self.region_h_spin.value()
            self.current_ocr_region = (x, y, w, h)
        else:
            x, y, w, h = (0.0, 67.0, 100.0, 33.0)
            self.current_ocr_region = (x, y, w, h)

        self.region_display_label.setText(f"Current Region: {x:.1f}%, {y:.1f}%, {w:.1f}%, {h:.1f}% (X, Y, Width, Height)")

    def closeEvent(self, event):
        """Handle window close"""
        if self.testing_worker and self.testing_worker.isRunning():
            self.stop_testing()
        event.accept()

    def on_splitter_moved(self, pos, index):
        """Handle splitter movement - save splitter state automatically"""
        # Save splitter state in real-time (with a small delay to avoid excessive saves)
        if hasattr(self, '_splitter_timer'):
            self._splitter_timer.stop()
        else:
            self._splitter_timer = QTimer()
            self._splitter_timer.setSingleShot(True)
            self._splitter_timer.timeout.connect(self.save_splitter_state)

        # Save after 500ms of no splitter activity
        self._splitter_timer.start(500)

    def save_splitter_state(self):
        """Save livestream testing GUI splitter state to settings"""
        try:
            settings = QSettings()

            if hasattr(self, 'content_splitter'):
                sizes = self.content_splitter.sizes()
                settings.setValue("layout/livestream_testing_splitter_sizes", sizes)
                settings.sync()
                print(f"💾 Livestream Testing splitter saved: {sizes}")

        except Exception as e:
            print(f"⚠️ Failed to save Livestream Testing splitter state: {e}")

    def load_splitter_state(self):
        """Load livestream testing GUI splitter state from settings"""
        try:
            settings = QSettings()

            if hasattr(self, 'content_splitter'):
                sizes = settings.value("layout/livestream_testing_splitter_sizes")
                if sizes:
                    try:
                        sizes = [int(size) for size in sizes]
                        self.content_splitter.setSizes(sizes)
                        print(f"📐 Livestream Testing splitter restored: {sizes}")
                    except (ValueError, TypeError):
                        print("📐 Using default Livestream Testing splitter sizes")

        except Exception as e:
            print(f"⚠️ Failed to load Livestream Testing splitter state: {e}")


def main():
    """Main function"""
    app = QApplication(sys.argv)

    window = LiveStreamTestingWindow()
    window.show()

    sys.exit(app.exec())


if __name__ == "__main__":
    main()
