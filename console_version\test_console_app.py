#!/usr/bin/env python3
"""
Test script for MFFUHijack Console Scanner
Verifies all components are working correctly
"""

import sys
import os

def test_imports():
    """Test if all required modules can be imported"""
    print("🔍 Testing imports...")
    
    try:
        # Test basic Python modules
        import json
        import threading
        from datetime import datetime
        print("✅ Basic Python modules: OK")
        
        # Test Tesseract
        import pytesseract
        from PIL import Image
        print("✅ Tesseract modules: OK")
        
        # Test OpenCV
        import cv2
        import numpy as np
        print("✅ OpenCV modules: OK")
        
        # Test console OCR module
        sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'MFFUHijack_Support'))
        from console_ocr import console_ocr, is_ocr_available
        print("✅ Console OCR module: OK")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_tesseract_setup():
    """Test Tesseract OCR setup"""
    print("\n🔧 Testing Tesseract setup...")
    
    try:
        import pytesseract
        import platform
        
        # Check Tesseract path
        if platform.system() == "Windows":
            tesseract_path = r"C:\Program Files\Tesseract-OCR\tesseract.exe"
            if os.path.exists(tesseract_path):
                pytesseract.pytesseract.tesseract_cmd = tesseract_path
                print(f"✅ Tesseract path found: {tesseract_path}")
            else:
                print("❌ Tesseract binary not found at expected location")
                return False
        
        # Test Tesseract version
        version = pytesseract.get_tesseract_version()
        print(f"✅ Tesseract version: {version}")
        
        return True
        
    except Exception as e:
        print(f"❌ Tesseract setup error: {e}")
        return False

def test_console_ocr():
    """Test console OCR functionality"""
    print("\n🎯 Testing console OCR...")
    
    try:
        sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'MFFUHijack_Support'))
        from console_ocr import console_ocr
        import cv2
        import numpy as np
        
        # Check if OCR is available
        if not console_ocr.is_available():
            print("❌ Console OCR not available")
            return False
        
        print("✅ Console OCR is available")
        
        # Create test image
        img = np.ones((100, 600, 3), dtype=np.uint8) * 255
        cv2.putText(img, "USE CODE: TEST123", (50, 50), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        
        # Test text extraction
        texts = console_ocr.extract_text_from_frame(img)
        print(f"✅ Text extraction: {len(texts)} texts found")
        for text in texts:
            print(f"   - '{text}'")
        
        # Test code detection
        codes = console_ocr.detect_codes_in_frame(img, ['Starter', 'Expert'])
        print(f"✅ Code detection: {len(codes)} codes found")
        for code, account_type in codes:
            print(f"   - Code: {code}, Type: {account_type}")
        
        return len(texts) > 0
        
    except Exception as e:
        print(f"❌ Console OCR test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_console_interface():
    """Test console interface components"""
    print("\n🖥️ Testing console interface...")
    
    try:
        # Import console application
        from mffuhijack_console import ConsoleInterface, HistoryManager, TesseractScanner
        
        # Test console interface
        console = ConsoleInterface()
        print("✅ ConsoleInterface created")
        
        # Test history manager
        history = HistoryManager()
        print("✅ HistoryManager created")
        
        # Test scanner
        scanner = TesseractScanner()
        if scanner.is_available():
            print("✅ TesseractScanner available")
        else:
            print("❌ TesseractScanner not available")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Console interface test error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_video_processing():
    """Test video processing capabilities"""
    print("\n🎬 Testing video processing...")
    
    try:
        import cv2
        import numpy as np
        
        # Create a simple test video frame
        frame = np.ones((480, 640, 3), dtype=np.uint8) * 255
        cv2.putText(frame, "STARTER ACCOUNTS USE CODE: START123", (50, 240), 
                   cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 0, 0), 2)
        
        print("✅ Test frame created")
        
        # Test frame processing
        sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'MFFUHijack_Support'))
        from console_ocr import console_ocr
        
        if console_ocr.is_available():
            codes = console_ocr.detect_codes_in_frame(frame, ['Starter'])
            print(f"✅ Frame processing: {len(codes)} codes detected")
            for code, account_type in codes:
                print(f"   - Code: {code}, Type: {account_type}")
            return True
        else:
            print("❌ OCR not available for frame processing")
            return False
        
    except Exception as e:
        print(f"❌ Video processing test error: {e}")
        return False

def main():
    """Main test function"""
    print("=" * 60)
    print("🧪 MFFUHIJACK CONSOLE SCANNER TEST")
    print("=" * 60)
    
    tests = [
        ("Module Imports", test_imports),
        ("Tesseract Setup", test_tesseract_setup),
        ("Console OCR", test_console_ocr),
        ("Console Interface", test_console_interface),
        ("Video Processing", test_video_processing),
    ]
    
    passed = 0
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        if test_func():
            passed += 1
            print(f"✅ {test_name}: PASSED")
        else:
            print(f"❌ {test_name}: FAILED")
    
    print(f"\n{'='*60}")
    print(f"🏁 TEST RESULTS: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("✅ All tests passed! Console scanner is ready to use.")
        print("🚀 Run: python mffuhijack_console.py")
    elif passed > 0:
        print("⚠️ Some tests passed. Check failed tests above.")
    else:
        print("❌ All tests failed. Check dependencies and installation.")
    
    print("="*60)
    
    return passed == len(tests)

if __name__ == "__main__":
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
