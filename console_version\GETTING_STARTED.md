# 🚀 Getting Started with MFFUHijack Console Scanner

## Quick Start (3 Steps)

### 1. Install Dependencies
Double-click: **`install_dependencies.bat`**
- Installs Python packages automatically
- Provides Tesseract installation instructions

### 2. Run Tests
Double-click: **`test_console.bat`**
- Verifies all components are working
- Should show: "5/5 tests passed"

### 3. Launch Application
Double-click: **`run_mffuhijack_console.bat`**
- Starts the console scanner
- Clean menu-driven interface

## 📋 What You'll See

### Main Menu
```
███╗   ███╗███████╗███████╗██╗   ██╗██╗  ██╗██╗     ██╗ █████╗  ██████╗██╗  ██╗
████╗ ████║██╔════╝██╔════╝██║   ██║██║  ██║██║     ██║██╔══██╗██╔════╝██║ ██╔╝
██╔████╔██║█████╗  █████╗  ██║   ██║███████║██║     ██║███████║██║     █████╔╝ 
██║╚██╔╝██║██╔══╝  ██╔══╝  ██║   ██║██╔══██║██║██   ██║██╔══██║██║     ██╔═██╗ 
██║ ╚═╝ ██║██║     ██║     ╚██████╔╝██║  ██║██║╚█████╔╝██║  ██║╚██████╗██║  ██╗
╚═╝     ╚═╝╚═╝     ╚═╝      ╚═════╝ ╚═╝  ╚═╝╚═╝ ╚════╝ ╚═╝  ╚═╝ ╚═════╝╚═╝  ╚═╝
                                                                                
                    Console Scanner v1.0 - Tesseract OCR Engine

================================================================================
                              MAIN MENU
================================================================================

  [1] Real-Time Scanner     - Scan live YouTube streams
  [2] Test Scanner          - Test with downloaded streams
  [3] Exit                  - Close application

================================================================================
```

### Scanning Dashboard
```
================================================================================
                           SCANNING DASHBOARD
================================================================================

Current Time:      2025-01-24 14:30:45
Runtime:           00:05:23
Frames Processed:  1,247
Codes Detected:    3
Codes Validated:   2

Code Types Found:
  Starter: 2
  Starter Plus: 1
  Expert: 0

================================================================================

[14:28:12] CODE DETECTED: START123 - Account Type: Starter
[14:29:45] CODE DETECTED: PLUS456 - Account Type: Starter Plus
[14:30:33] CODE DETECTED: RESET789 - Account Type: Starter
```

## 🎯 Usage Examples

### Real-Time Scanning
1. Select option **[1]** from main menu
2. Enter YouTube livestream URL
3. Select account types: **1,2,3** (Starter, Starter Plus, Expert)
4. Enter credentials (saved automatically for next time)
5. Choose **Y** to preload browser pages (recommended)
6. Choose **Y** to start scanning
7. Watch the dashboard for code detections and automatic submissions
8. Press **Ctrl+C** to stop

### Test Scanning
1. Place video files in **`test_streams/`** folder
2. Select option **[2]** from main menu
3. Choose **[1]** to select existing file
4. Pick your video file
5. Set scan interval: **5** (seconds)
6. Set start time: **01:30:00** (HH:MM:SS)
7. Select account types: **1,3** (Starter, Expert)
8. Use saved credentials or enter new ones
9. Choose **Y** to preload browser for code validation
10. Choose **Y** to start test scanning

## 🔗 GUI Integration

### Automatic Credential Import
If you've used the main MFFUHijack GUI application, the console version will automatically detect and use your saved credentials:

```
✅ Found GUI credentials for: your_username
✅ Found saved credentials for: your_username (from GUI version)
  [1] Use saved credentials  ← Automatically uses GUI credentials!
  [2] Enter new credentials
  [3] Clear saved credentials
```

### How It Works
- **Seamless**: No setup required, automatically detects GUI credentials
- **Priority**: Console credentials override GUI credentials if both exist
- **Independent**: Clearing console credentials doesn't affect GUI version
- **Transparent**: Shows you which version the credentials came from

## 🔧 Configuration

### Video Files
- Supported: MP4, MKV, AVI, MOV
- Place in: `test_streams/` directory
- Any resolution/quality

### Account Types
- **Starter**: Basic account codes
- **Starter Plus**: Enhanced account codes  
- **Expert**: Professional account codes

### Scan Settings
- **Interval**: How often to scan (seconds)
- **Start Time**: When to begin in video (HH:MM:SS)
- **Full Frame**: Scans entire video (not just bottom)

## 📊 Features

### ✅ What Works
- **Tesseract OCR**: Exclusive engine, optimized for accuracy
- **Full Frame Scanning**: Entire video analyzed
- **Character Correction**: Automatic S/5 confusion fixes
- **Real-Time Dashboard**: Live stats without scrolling
- **Persistent History**: All detections saved with timestamps
- **Smart Credentials**: Auto-imports from GUI version, no repetitive entry
- **Browser Preloading**: EXACT same as GUI version, silent selenium operation
- **Clean Interface**: Console clearing, structured menus

### 🎯 Detection Patterns
- `STARTER ACCOUNTS USE CODE: ABC123`
- `FREE 50 STARTER ACCOUNTS USE CODE: START123`
- `STARTER PLUS ACCOUNTS USE CODE: PLUS456`
- `EXPERT ACCOUNTS USE CODE: EXPERT789`
- Generic `CODE: XYZ123` patterns

## 🐛 Troubleshooting

### If Tests Fail
1. **Install Python 3.8+**: [python.org](https://www.python.org/downloads/)
2. **Install Tesseract**: [Windows Download](https://github.com/UB-Mannheim/tesseract/wiki)
3. **Run**: `install_dependencies.bat`
4. **Retry**: `test_console.bat`

### If No Codes Detected
- Check video quality and text visibility
- Verify account type selection matches content
- Ensure text is clearly readable in video
- Try different scan intervals

### Console Issues
- Use Windows Command Prompt or PowerShell
- Ensure window is wide enough (80+ characters)
- Check Unicode support in terminal

## 📁 File Overview

```
console_version/
├── 🚀 run_mffuhijack_console.bat    # MAIN LAUNCHER
├── 🧪 test_console.bat              # TEST SUITE
├── 📦 install_dependencies.bat      # DEPENDENCY INSTALLER
├── 📄 README.md                     # Full documentation
├── 📋 requirements.txt              # Python packages
├── 🐍 mffuhijack_console.py         # Main application
├── 🧪 test_console_app.py           # Test suite
├── MFFUHijack_Support/
│   └── 🔍 console_ocr.py            # OCR engine
├── 📁 test_streams/                 # Video files go here
├── 📊 scan_history.json             # Detection history (auto-created)
└── 🔐 user_credentials.json         # Saved login credentials (auto-created)
```

## 🎉 You're Ready!

The console scanner is fully functional and ready for livestream code detection. 

**Start with**: `run_mffuhijack_console.bat`

**Need help?** Check `README.md` for detailed documentation.

---

**MFFUHijack Console Scanner v1.0**  
*Clean • Fast • Reliable*
