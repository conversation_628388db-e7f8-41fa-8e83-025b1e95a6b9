#!/usr/bin/env python3
"""
Test script for Tesseract OCR integration in MFFUHijack
Tests the new TesseractOCREngine class and compares with EasyOCR
"""

import sys
import os
import numpy as np
import cv2

# Add support folder to path
sys.path.append(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'MFFUHijack_Support'))

def create_test_image():
    """Create a test image with typical livestream text"""
    # Create a white background image
    image = np.ones((200, 800, 3), dtype=np.uint8) * 255
    
    # Add text similar to what we'd see in livestreams
    test_texts = [
        "FREE 50 STARTER ACCOUNTS USE CODE: START123",
        "x5 FREE RESETS USE CODE: RESET5J",
        "EXPERT ACCOUNTS USE CODE: EXPERT99"
    ]
    
    y_positions = [50, 100, 150]
    
    for i, text in enumerate(test_texts):
        cv2.putText(image, text, (50, y_positions[i]), 
                   cv2.FONT_HERSHEY_SIMPLEX, 0.8, (0, 0, 0), 2)
    
    return image

def test_tesseract_availability():
    """Test if Tesseract is available and working"""
    print("🔍 Testing Tesseract OCR availability...")
    
    try:
        import pytesseract
        from PIL import Image
        
        # Test basic Tesseract functionality
        version = pytesseract.get_tesseract_version()
        print(f"✅ Tesseract version: {version}")
        return True
        
    except ImportError as e:
        print(f"❌ Tesseract import failed: {e}")
        print("   Install with: pip install pytesseract")
        print("   Also install Tesseract binary:")
        print("   Windows: https://github.com/UB-Mannheim/tesseract/wiki")
        print("   macOS: brew install tesseract")
        print("   Linux: sudo apt install tesseract-ocr")
        return False
    except Exception as e:
        print(f"❌ Tesseract test failed: {e}")
        print("   Make sure Tesseract binary is installed and in PATH")
        return False

def test_ocr_manager():
    """Test the OCR manager with Tesseract"""
    print("\n🔧 Testing OCR Manager with Tesseract...")
    
    try:
        from ocr_utils import ocr_manager
        
        # Check available engines
        print(f"Available engines: {list(ocr_manager.engines.keys())}")
        print(f"Current engine: {ocr_manager.current_engine}")
        
        # Test Tesseract engine specifically
        if 'Tesseract' in ocr_manager.engines:
            tesseract_engine = ocr_manager.engines['Tesseract']
            print(f"Tesseract available: {tesseract_engine.is_available()}")
            
            if tesseract_engine.is_available():
                print("✅ Tesseract engine is ready")
                return True
            else:
                print("❌ Tesseract engine not available")
                return False
        else:
            print("❌ Tesseract engine not found in OCR manager")
            return False
            
    except ImportError as e:
        print(f"❌ OCR manager import failed: {e}")
        return False
    except Exception as e:
        print(f"❌ OCR manager test failed: {e}")
        return False

def test_text_extraction():
    """Test text extraction with Tesseract"""
    print("\n📝 Testing text extraction...")
    
    try:
        from ocr_utils import ocr_manager
        
        # Create test image
        test_image = create_test_image()
        print(f"Created test image: {test_image.shape}")
        
        # Set engine to Tesseract
        if ocr_manager.set_engine('Tesseract'):
            print("✅ Switched to Tesseract engine")
        else:
            print("❌ Failed to switch to Tesseract engine")
            return False
        
        # Extract text
        print("🔍 Extracting text with Tesseract...")
        results = ocr_manager.extract_text_from_image(test_image, region="full")
        
        print(f"📊 Tesseract found {len(results)} text regions:")
        for i, result in enumerate(results):
            text = result.get('text', '')
            confidence = result.get('confidence', 0)
            print(f"   {i+1}. '{text}' (confidence: {confidence:.3f})")
        
        # Test code detection
        print("\n🎯 Testing code detection...")
        codes = ocr_manager.detect_codes_in_image(test_image, region="full")
        
        print(f"📊 Found {len(codes)} codes:")
        for i, code in enumerate(codes):
            print(f"   {i+1}. {code}")
        
        return len(results) > 0
        
    except Exception as e:
        print(f"❌ Text extraction test failed: {e}")
        return False

def compare_ocr_engines():
    """Compare Tesseract with EasyOCR if available"""
    print("\n⚖️ Comparing OCR engines...")
    
    try:
        from ocr_utils import ocr_manager
        
        # Create test image
        test_image = create_test_image()
        
        engines_to_test = []
        if 'Tesseract' in ocr_manager.engines and ocr_manager.engines['Tesseract'].is_available():
            engines_to_test.append('Tesseract')
        if 'EasyOCR' in ocr_manager.engines and ocr_manager.engines['EasyOCR'].is_available():
            engines_to_test.append('EasyOCR')
        
        if not engines_to_test:
            print("❌ No OCR engines available for comparison")
            return False
        
        results_comparison = {}
        
        for engine_name in engines_to_test:
            print(f"\n🔍 Testing {engine_name}...")
            
            if ocr_manager.set_engine(engine_name):
                start_time = time.time()
                results = ocr_manager.extract_text_from_image(test_image, region="full")
                end_time = time.time()
                
                processing_time = end_time - start_time
                
                results_comparison[engine_name] = {
                    'results': results,
                    'time': processing_time,
                    'text_count': len(results)
                }
                
                print(f"   📊 {engine_name}: {len(results)} results in {processing_time:.3f}s")
                for result in results:
                    text = result.get('text', '')
                    confidence = result.get('confidence', 0)
                    print(f"      '{text}' (conf: {confidence:.3f})")
            else:
                print(f"   ❌ Failed to switch to {engine_name}")
        
        # Summary
        print(f"\n📋 Comparison Summary:")
        for engine_name, data in results_comparison.items():
            print(f"   {engine_name}: {data['text_count']} results, {data['time']:.3f}s")
        
        return True
        
    except Exception as e:
        print(f"❌ Engine comparison failed: {e}")
        return False

def main():
    """Main test function"""
    print("=" * 60)
    print("🧪 TESSERACT OCR INTEGRATION TEST")
    print("=" * 60)
    
    # Import time for performance testing
    import time
    
    tests_passed = 0
    total_tests = 4
    
    # Test 1: Tesseract availability
    if test_tesseract_availability():
        tests_passed += 1
    
    # Test 2: OCR manager integration
    if test_ocr_manager():
        tests_passed += 1
    
    # Test 3: Text extraction
    if test_text_extraction():
        tests_passed += 1
    
    # Test 4: Engine comparison
    if compare_ocr_engines():
        tests_passed += 1
    
    # Final results
    print("\n" + "=" * 60)
    print(f"🏁 TEST RESULTS: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("✅ All tests passed! Tesseract OCR is ready to use.")
        print("🎯 Tesseract is now the default OCR engine for MFFUHijack.")
    elif tests_passed > 0:
        print("⚠️ Some tests passed. Check the errors above.")
    else:
        print("❌ All tests failed. Tesseract OCR is not working properly.")
    
    print("=" * 60)
    
    return tests_passed == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
