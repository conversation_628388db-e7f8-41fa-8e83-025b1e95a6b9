# MFFUHijack Console Scanner

A clean, console-only version of MFFUHijack with Tesseract OCR exclusively. Features a menu-driven interface for real-time and test scanning of livestream codes.

## 🚀 Quick Start

### Run the Application
```batch
run_mffuhijack_console.bat
```

### Run Tests
```batch
test_console.bat
```

## 📁 File Structure

```
console_version/
├── run_mffuhijack_console.bat    # Main launcher (double-click to run)
├── test_console.bat              # Test suite launcher
├── mffuhijack_console.py         # Main console application
├── test_console_app.py           # Comprehensive test suite
├── MFFUHijack_Support/
│   └── console_ocr.py            # Tesseract OCR engine
├── test_streams/                 # Directory for video files
├── scan_history.json             # Persistent detection history (auto-created)
└── user_credentials.json         # Saved login credentials (auto-created)
```

## ✨ Features

### 🎯 Core Functionality
- **Tesseract OCR Only**: Optimized single-engine implementation
- **Full Frame Scanning**: Scans entire video frame (not just bottom portion)
- **PSM 7 Configuration**: Best character recognition, avoids 1/l confusion
- **Clean Console Interface**: No persistent logging, clear menu navigation
- **Persistent History**: Stores all detected codes with timestamps
- **Smart Credentials**: Saves login credentials, no repetitive entry required
- **Browser Preloading**: Automated browser setup for instant code submission

### 📺 Scanning Modes

#### Real-Time Scanner
- YouTube livestream URL input
- Multi-select account types (Starter, Starter Plus, Expert)
- Smart credential management (saves login info)
- Browser preloading with user confirmation
- Automatic code submission to preloaded pages
- Live scanning dashboard with real-time stats

#### Test Scanner
- File selection from `test_streams` directory
- Configurable scan intervals (default: 5 seconds)
- Start time specification (HH:MM:SS format)
- Video file processing with frame-by-frame analysis
- Browser preloading for code validation (no checkout)
- Automatic credential loading from saved data

### 🎨 Interface Design
- ASCII banner branding
- Numbered menu navigation
- Console clearing between states
- Real-time dashboard updates
- Clean, structured displays

## 📋 Prerequisites

### Required Software
1. **Python 3.8+** - [Download from python.org](https://www.python.org/downloads/)
2. **Tesseract OCR** - [Download for Windows](https://github.com/UB-Mannheim/tesseract/wiki)

### Python Packages
```bash
pip install pytesseract opencv-python pillow numpy
```

## 🔧 Installation

1. **Download/Extract** the console_version folder
2. **Install Python** if not already installed
3. **Install Tesseract OCR** binary for your operating system
4. **Install Python packages** using pip (see above)
5. **Double-click** `run_mffuhijack_console.bat` to launch

## 🧪 Testing

### Verify Installation
Double-click `test_console.bat` or run:
```bash
python test_console_app.py
```

### Expected Test Results
```
🏁 TEST RESULTS: 5/5 tests passed
✅ All tests passed! Console scanner is ready to use.
```

## 🎮 Usage Guide

### Main Menu
1. **Real-Time Scanner** - Scan live YouTube streams
2. **Test Scanner** - Test with downloaded video files
3. **Exit** - Close application

### Real-Time Scanner Setup
1. Enter YouTube livestream URL
2. Select account types to scan for (1,2,3)
3. Enter account credentials
4. Choose: Start scanning, View history, or Return to menu

### Test Scanner Setup
1. Select existing video file or download new stream
2. Configure scan interval (seconds) and start time (HH:MM:SS)
3. Select account types to scan for
4. Enter account credentials
5. Choose: Start scanning, View history, or Return to menu

### Scanning Interface
- **Clean Dashboard**: Shows current time, frames processed, codes detected
- **Real-Time Updates**: Live statistics without scrolling text
- **Detection Alerts**: Timestamped code discoveries with account type
- **Account Type Breakdown**: Counts by Starter/Starter Plus/Expert
- **Ctrl+C**: Stop scanning and return to menu

## 🔍 Code Detection

### Supported Patterns
- `STARTER ACCOUNTS USE CODE: ABC123`
- `FREE 50 STARTER ACCOUNTS USE CODE: START123`
- `STARTER PLUS ACCOUNTS USE CODE: PLUS456`
- `EXPERT ACCOUNTS USE CODE: EXPERT789`
- Generic `CODE: XYZ123` patterns

### Account Types
- **Starter**: Basic account codes
- **Starter Plus**: Enhanced account codes
- **Expert**: Professional account codes

### Character Correction
- Automatic S/5 confusion correction
- Context-based character recognition
- Optimized for livestream text quality

## 📊 Scan History

All detected codes are automatically saved with:
- Timestamp of detection
- Code value
- Account type
- Source (URL or filename)
- Validation status

Access via menu option 2 in any scanner mode.

## 🌐 Browser Preloading

### Automated Browser Setup
Before starting any scan, the console scanner offers browser preloading for instant code submission:

#### Real-Time Scanner Flow
1. **Configuration**: Enter URL, select account types, credentials
2. **Browser Preloading Prompt**: "Preload browser pages? (Y/N)"
3. **If Yes**: Automatic browser initialization, login, and page preloading
4. **Confirmation**: "Start scanning now? (Y/N)"
5. **Scanning**: Codes automatically submitted to preloaded pages

#### Test Scanner Flow
1. **Configuration**: Select video, set intervals, select account types
2. **Browser Preloading Prompt**: "Preload browser pages? (Y/N)"
3. **If Yes**: Browser setup for code validation (no checkout)
4. **Confirmation**: "Start test scanning now? (Y/N)"
5. **Scanning**: Codes validated in browser without purchase

#### Browser Preloading Process
```
🌐 BROWSER PRELOADING
==========================================
🌐 Initializing browser automation...
✅ Login initiated - complete CAPTCHA if present
✅ Login completed successfully
🚀 Preloading pages for selected types: Starter, Expert
✅ Selected account pages preloaded to checkout stage
💳 Coupon textboxes focused and ready for instant code entry

🎯 Browser is ready for instant code submission
💡 Keep browser window open during scanning
```

#### EXACT GUI Compatibility
- ✅ **Same Global Instance**: Uses `browser_automation` global instance
- ✅ **Identical Workflow**: login → wait_for_login_completion → open_manual_tabs
- ✅ **Same Methods**: All GUI browser methods available in console
- ✅ **Silent Operation**: All selenium logging suppressed (no info/warning/error messages)
- ✅ **Same Parameters**: Identical method signatures and parameters

#### Benefits
- ✅ **Instant Submission**: Codes submitted immediately when detected
- ✅ **Pre-authenticated**: Login completed before scanning starts
- ✅ **Multi-Account**: All selected account types preloaded
- ✅ **User Control**: Optional - can skip if not needed
- ✅ **Test Mode**: Validation without purchase for test scanning

## 🔐 Credentials Management

### Smart Credential System
The console scanner automatically manages your login credentials from both GUI and console versions:

#### Automatic Detection
The console scanner checks for credentials in this order:
1. **GUI Version Credentials**: Automatically imports from main MFFUHijack GUI
2. **Console Credentials**: Uses console-specific saved credentials
3. **Manual Entry**: Prompts for new credentials if none found

#### First Time Usage
1. If GUI credentials exist, they're automatically detected
2. Otherwise, enter username and password when prompted
3. Choose "y" when asked to save credentials for console use

#### Subsequent Usage
When credentials are found, you'll see:
```
✅ Found saved credentials for: your_username (from GUI version)
  [1] Use saved credentials
  [2] Enter new credentials
  [3] Clear saved credentials
```

#### Options
- **Option 1**: Use saved credentials (no typing required)
- **Option 2**: Enter new credentials (saves to console storage)
- **Option 3**: Clear console credentials (GUI credentials remain intact)

#### Benefits
- ✅ **Seamless Integration**: Automatically uses GUI credentials
- ✅ **No Repetitive Entry**: Credentials shared between versions
- ✅ **Priority System**: Console credentials override GUI when both exist
- ✅ **Source Transparency**: Shows whether credentials are from GUI or console
- ✅ **Independent Management**: Clear console credentials without affecting GUI

#### Credential Sources
- **GUI Version**: Stored in system registry/settings (QSettings)
- **Console Version**: Stored in `user_credentials.json`
- **Priority**: Console credentials take precedence over GUI credentials

### Security Notes
- GUI credentials stored in system settings (Windows Registry)
- Console credentials stored in local JSON file
- Consider additional encryption for production use
- Console credential file is excluded from version control

## ⚙️ Configuration

### Video Files
Place video files in the `test_streams/` directory. Supported formats:
- MP4, MKV, AVI, MOV

### OCR Settings
Modify `MFFUHijack_Support/console_ocr.py` to adjust:
- Detection patterns
- Confidence thresholds
- Preprocessing options

## 🐛 Troubleshooting

### Common Issues

#### "Tesseract OCR not available"
- Install Tesseract binary: [Windows Download](https://github.com/UB-Mannheim/tesseract/wiki)
- Ensure it's installed to: `C:\Program Files\Tesseract-OCR\`
- Add to system PATH if needed

#### "Video processing not available"
- Install OpenCV: `pip install opencv-python`
- Verify: `python -c "import cv2; print(cv2.__version__)"`

#### "No codes detected"
- Check video quality and text visibility
- Verify account type selection matches stream content
- Test with known working video files
- Ensure text is clearly visible in video

#### Console display issues
- Use terminal with proper Unicode support
- Ensure console window is wide enough (80+ characters)
- Try Windows Command Prompt or PowerShell

### Debug Information
Run the test suite for detailed diagnostics:
```batch
test_console.bat
```

## 🔄 Differences from GUI Version

### Removed Components
- ❌ EasyOCR and PaddleOCR engines
- ❌ PyQt6 GUI components
- ❌ Browser automation
- ❌ Live preview windows
- ❌ Complex settings management

### Enhanced Features
- ✅ Cleaner, faster interface
- ✅ Optimized single-engine performance
- ✅ Better console output management
- ✅ Simplified configuration
- ✅ Reduced memory footprint
- ✅ Batch file launchers

## 📈 Performance

### Optimizations
- **Single OCR Engine**: Eliminates engine switching overhead
- **Frame Skipping**: Configurable intervals for efficient processing
- **Memory Management**: Proper video capture cleanup
- **Error Handling**: Robust exception handling without crashes

### System Requirements
- **CPU**: Any modern processor (Tesseract is CPU-based)
- **RAM**: 2GB minimum, 4GB recommended
- **Storage**: 100MB for application, additional space for video files
- **OS**: Windows 10/11, macOS, Linux

## 📄 License

Copyright (c) 2025 jordan. All rights reserved.

---

**MFFUHijack Console Scanner v1.0**  
*Tesseract OCR Engine - Clean Menu Interface*

🎯 **Ready for livestream code detection!**
